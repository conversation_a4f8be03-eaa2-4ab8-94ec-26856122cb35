{"name": "ping-pong", "version": "0.1.0", "description": "Crypto auto-trading platform", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --coverage", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:docker": "docker-compose run --rm app npm run test", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:generate": "npm run typeorm -- -d ./typeorm.config.ts migration:generate ./src/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./src/migrations/$npm_config_name", "migration:run": "npm run typeorm -- -d ./typeorm.config.ts migration:run", "migration:revert": "npm run typeorm -- -d ./typeorm.config.ts migration:revert", "db:migrate": "ts-node -r tsconfig-paths/register src/config/database.init.ts", "db:drop": "ts-node -r tsconfig-paths/register src/scripts/drop-database.ts"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/passport-local": "^1.0.38", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "express": "^5.1.0", "handlebars": "^4.7.8", "nest-winston": "^1.10.2", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.23", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/handlebars": "^4.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.17", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.15.1", "@types/supertest": "^6.0.3", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-simple-import-sort": "^12.1.1", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}