{"info": {"name": "PingPong Trading API", "description": "API collection for PingPong Trading Platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}}}]}, {"name": "Strategies", "item": [{"name": "Get All Strategies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/strategies", "host": ["{{baseUrl}}"], "path": ["strategies"]}}}, {"name": "Get Strategy by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/strategies/:id", "host": ["{{baseUrl}}"], "path": ["strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Create Strategy (Admin Only)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/strategies", "host": ["{{baseUrl}}"], "path": ["strategies"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"BTC/USD Strategy\",\n    \"buyAmount\": 100.00,\n    \"profitPercentage\": 2.50,\n    \"exchange\": \"kraken\",\n    \"productId\": 1\n}"}}}, {"name": "Update Strategy (Admin Only)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/strategies/:id", "host": ["{{baseUrl}}"], "path": ["strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated BTC/USD Strategy\",\n    \"buyAmount\": 150.00,\n    \"profitPercentage\": 3.00\n}"}}}, {"name": "Delete Strategy (Admin Only)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/strategies/:id", "host": ["{{baseUrl}}"], "path": ["strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}}}]}, {"name": "Subscriptions", "item": [{"name": "Get All Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/subscriptions", "host": ["{{baseUrl}}"], "path": ["subscriptions"]}}}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/subscriptions/:id", "host": ["{{baseUrl}}"], "path": ["subscriptions", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/subscriptions", "host": ["{{baseUrl}}"], "path": ["subscriptions"]}, "body": {"mode": "raw", "raw": "{\n    \"strategyId\": 1,\n    \"apiKeyId\": 1,\n    \"paymentId\": 1,\n    \"startDate\": \"2024-03-10T00:00:00Z\",\n    \"endDate\": \"2024-04-10T00:00:00Z\"\n}"}}}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/subscriptions/:id", "host": ["{{baseUrl}}"], "path": ["subscriptions", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n    \"status\": \"paused\",\n    \"endDate\": \"2024-05-10T00:00:00Z\"\n}"}}}, {"name": "Delete Subscription", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/subscriptions/:id", "host": ["{{baseUrl}}"], "path": ["subscriptions", ":id"], "variable": [{"key": "id", "value": "1"}]}}}]}, {"name": "API Keys", "item": [{"name": "Get All API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api-keys", "host": ["{{baseUrl}}"], "path": ["api-keys"]}}}, {"name": "Get API Key by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["api-keys", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api-keys", "host": ["{{baseUrl}}"], "path": ["api-keys"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Kraken API Key\",\n    \"apiKey\": \"your-api-key\",\n    \"apiSecret\": \"your-api-secret\",\n    \"exchange\": \"kraken\"\n}"}}}, {"name": "Update API Key", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["api-keys", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Kraken API Key\",\n    \"apiKey\": \"new-api-key\",\n    \"apiSecret\": \"new-api-secret\"\n}"}}}, {"name": "Delete API Key", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["api-keys", ":id"], "variable": [{"key": "id", "value": "1"}]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "your-jwt-token-here", "type": "string"}]}