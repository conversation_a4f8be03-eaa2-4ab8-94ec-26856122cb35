{"name": "pingpong-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "15.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "@types/node": "^20.17.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "typescript": "^5.8.3", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "eslint": "^8.57.1", "eslint-config-next": "15.5.3", "@heroicons/react": "^2.2.0", "axios": "^1.9.0", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-hook-form": "^7.54.2", "@hookform/resolvers": "^3.10.0", "zod": "^3.24.1", "recharts": "^2.15.0", "date-fns": "^4.1.0", "clsx": "^2.1.1", "lucide-react": "^0.468.0"}, "devDependencies": {"@types/node": "^20.17.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "eslint": "^8.57.1", "eslint-config-next": "15.5.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}