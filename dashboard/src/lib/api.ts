import { <PERSON><PERSON><PERSON><PERSON>, AuthResponse, Order, Product, Step, Strategy, Subscription, User } from "@/types";
import axios from "axios";
import Cookies from "js-cookie";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(config => {
  const token = Cookies.get("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor to handle auth errors
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      Cookies.remove("token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  },
);

// Auth API
export const authApi = {
  login: async (email: string, password: string): Promise<AuthResponse> => {
    const response = await api.post("/auth/login", { email, password });
    return response.data.data; // Extract data from wrapped response
  },

  register: async (email: string, password: string): Promise<{ message: string }> => {
    const response = await api.post("/auth/register", { email, password });
    return response.data.data; // Extract data from wrapped response
  },

  verifyEmail: async (email: string, code: string): Promise<{ message: string }> => {
    const response = await api.post("/auth/verify-email", { email, code });
    return response.data.data; // Extract data from wrapped response
  },

  resendVerification: async (email: string): Promise<{ message: string }> => {
    const response = await api.post("/auth/resend-verification", { email });
    return response.data.data; // Extract data from wrapped response
  },

  getProfile: async (): Promise<User> => {
    const response = await api.get("/auth/profile");
    return response.data.data; // Extract data from wrapped response
  },

  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await api.put("/auth/profile", data);
    return response.data.data; // Extract data from wrapped response
  },
};

// Products API
export const productsApi = {
  getAll: async (): Promise<Product[]> => {
    const response = await api.get("/products");
    return response.data.data; // Extract data from wrapped response
  },

  getById: async (id: number): Promise<Product> => {
    const response = await api.get(`/products/${id}`);
    return response.data.data; // Extract data from wrapped response
  },
};

// API Keys API
export const apiKeysApi = {
  getAll: async (): Promise<ApiKey[]> => {
    const response = await api.get("/api-keys");
    return response.data.data; // Extract data from wrapped response
  },

  create: async (data: {
    exchange: string;
    keyName: string;
    apiKey: string;
    apiSecret: string;
  }): Promise<ApiKey> => {
    const response = await api.post("/api-keys", data);
    return response.data.data; // Extract data from wrapped response
  },

  update: async (id: number, data: Partial<ApiKey>): Promise<ApiKey> => {
    const response = await api.patch(`/api-keys/${id}`, data);
    return response.data.data; // Extract data from wrapped response
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/api-keys/${id}`);
    // Delete operations typically don't return data
  },
};

// Strategies API
export const strategiesApi = {
  getAll: async (): Promise<Strategy[]> => {
    const response = await api.get("/strategies");
    return response.data.data; // Extract data from wrapped response
  },

  create: async (data: Partial<Strategy>): Promise<Strategy> => {
    const response = await api.post("/strategies", data);
    return response.data.data; // Extract data from wrapped response
  },

  update: async (id: number, data: Partial<Strategy>): Promise<Strategy> => {
    const response = await api.patch(`/strategies/${id}`, data);
    return response.data.data; // Extract data from wrapped response
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/strategies/${id}`);
    // Delete operations typically don't return data
  },
};

// Steps API
export const stepsApi = {
  getAll: async (): Promise<Step[]> => {
    const response = await api.get("/steps");
    return response.data.data; // Extract data from wrapped response
  },

  getByProduct: async (productId: number): Promise<Step[]> => {
    const response = await api.get(`/steps/product/${productId}`);
    return response.data.data; // Extract data from wrapped response
  },

  create: async (data: Partial<Step>): Promise<Step> => {
    const response = await api.post("/steps", data);
    return response.data.data; // Extract data from wrapped response
  },

  update: async (id: number, data: Partial<Step>): Promise<Step> => {
    const response = await api.patch(`/steps/${id}`, data);
    return response.data.data; // Extract data from wrapped response
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/steps/${id}`);
    // Delete operations typically don't return data
  },
};

// Orders API
export const ordersApi = {
  getAll: async (): Promise<Order[]> => {
    const response = await api.get("/orders");
    return response.data.data; // Extract data from wrapped response
  },

  getByStrategy: async (strategyId: number): Promise<Order[]> => {
    const response = await api.get(`/orders/strategy/${strategyId}`);
    return response.data.data; // Extract data from wrapped response
  },
};

// Subscriptions API
export const subscriptionsApi = {
  getAll: async (): Promise<Subscription[]> => {
    const response = await api.get("/subscriptions");
    return response.data.data; // Extract data from wrapped response
  },

  create: async (productId: number): Promise<{ url: string }> => {
    const response = await api.post("/subscriptions", { productId });
    return response.data.data; // Extract data from wrapped response
  },

  cancel: async (id: number): Promise<void> => {
    await api.delete(`/subscriptions/${id}`);
    // Delete operations typically don't return data
  },
};

export default api;
