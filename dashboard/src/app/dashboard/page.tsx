'use client';

import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { 
  CubeIcon, 
  KeyIcon, 
  ChartBarIcon, 
  ClockIcon 
} from '@heroicons/react/24/outline';
import { 
  productsApi, 
  apiKeysApi, 
  strategiesApi, 
  ordersApi 
} from '@/lib/api';

interface DashboardStats {
  products: number;
  apiKeys: number;
  strategies: number;
  orders: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    products: 0,
    apiKeys: 0,
    strategies: 0,
    orders: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [products, apiKeys, strategies, orders] = await Promise.all([
          productsApi.getAll(),
          apiKeysApi.getAll(),
          strategiesApi.getAll(),
          ordersApi.getAll(),
        ]);

        setStats({
          products: products.length,
          apiKeys: apiKeys.length,
          strategies: strategies.length,
          orders: orders.length,
        });
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      name: 'Products',
      value: stats.products,
      icon: CubeIcon,
      color: 'bg-blue-500',
      href: '/dashboard/products',
    },
    {
      name: 'API Keys',
      value: stats.apiKeys,
      icon: KeyIcon,
      color: 'bg-green-500',
      href: '/dashboard/api-keys',
    },
    {
      name: 'Strategies',
      value: stats.strategies,
      icon: ChartBarIcon,
      color: 'bg-purple-500',
      href: '/dashboard/strategies',
    },
    {
      name: 'Orders',
      value: stats.orders,
      icon: ClockIcon,
      color: 'bg-orange-500',
      href: '/dashboard/orders',
    },
  ];

  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Welcome to PingPong Dashboard
            </h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                Manage your crypto auto-trading strategies, API keys, and monitor your trading performance.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card) => (
            <div
              key={card.name}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => window.location.href = card.href}
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${card.color} rounded-md p-3`}>
                      <card.icon className="h-6 w-6 text-white" aria-hidden="true" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {card.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {loading ? '...' : card.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Quick Actions
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <button
                onClick={() => window.location.href = '/dashboard/api-keys'}
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                    <KeyIcon className="h-6 w-6" aria-hidden="true" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Add API Key
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Connect your exchange account to start trading
                  </p>
                </div>
              </button>

              <button
                onClick={() => window.location.href = '/dashboard/strategies'}
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                    <ChartBarIcon className="h-6 w-6" aria-hidden="true" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Create Strategy
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Set up your automated trading strategy
                  </p>
                </div>
              </button>

              <button
                onClick={() => window.location.href = '/dashboard/products'}
                className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-primary-500 rounded-lg border border-gray-300 hover:border-gray-400"
              >
                <div>
                  <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                    <CubeIcon className="h-6 w-6" aria-hidden="true" />
                  </span>
                </div>
                <div className="mt-8">
                  <h3 className="text-lg font-medium">
                    <span className="absolute inset-0" aria-hidden="true" />
                    Browse Products
                  </h3>
                  <p className="mt-2 text-sm text-gray-500">
                    Explore available crypto products
                  </p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
