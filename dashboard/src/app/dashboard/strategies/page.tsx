'use client';

import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { PlusIcon, TrashIcon, PencilIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';
import { strategiesApi, productsApi } from '@/lib/api';
import { Strategy, Product } from '@/types';

export default function StrategiesPage() {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    buyAmount: '',
    profitPercentage: '',
    exchange: 'kraken' as 'kraken' | 'kucoin',
    productId: '',
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [strategiesData, productsData] = await Promise.all([
        strategiesApi.getAll(),
        productsApi.getAll(),
      ]);
      setStrategies(strategiesData);
      setProducts(productsData);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const data = {
        ...formData,
        buyAmount: parseFloat(formData.buyAmount),
        profitPercentage: parseFloat(formData.profitPercentage),
        productId: parseInt(formData.productId),
      };

      if (editingStrategy) {
        await strategiesApi.update(editingStrategy.id, data);
      } else {
        await strategiesApi.create(data);
      }
      await fetchData();
      setShowModal(false);
      setEditingStrategy(null);
      setFormData({
        name: '',
        buyAmount: '',
        profitPercentage: '',
        exchange: 'kraken',
        productId: '',
      });
    } catch (error) {
      console.error('Failed to save strategy:', error);
    }
  };

  const handleEdit = (strategy: Strategy) => {
    setEditingStrategy(strategy);
    setFormData({
      name: strategy.name,
      buyAmount: strategy.buyAmount.toString(),
      profitPercentage: strategy.profitPercentage.toString(),
      exchange: strategy.exchange,
      productId: strategy.productId.toString(),
    });
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (confirm('Are you sure you want to delete this strategy?')) {
      try {
        await strategiesApi.delete(id);
        await fetchData();
      } catch (error) {
        console.error('Failed to delete strategy:', error);
      }
    }
  };

  const toggleStrategy = async (strategy: Strategy) => {
    try {
      await strategiesApi.update(strategy.id, { isActive: !strategy.isActive });
      await fetchData();
    } catch (error) {
      console.error('Failed to toggle strategy:', error);
    }
  };

  return (
    <DashboardLayout title="Strategies">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Trading Strategies</h2>
            <p className="text-gray-600">Create and manage your automated trading strategies</p>
          </div>
          <button
            onClick={() => setShowModal(true)}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Strategy</span>
          </button>
        </div>

        {/* Strategies List */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="p-6 text-center">Loading...</div>
          ) : strategies.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No strategies found. Create your first strategy to get started.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strategy
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Exchange
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Buy Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Profit %
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {strategies.map((strategy) => (
                    <tr key={strategy.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{strategy.name}</div>
                        <div className="text-sm text-gray-500">
                          Created {new Date(strategy.createdAt).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {strategy.product?.name || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {strategy.product?.symbol || 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                        {strategy.exchange}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${strategy.buyAmount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {strategy.profitPercentage}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            strategy.isActive
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {strategy.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => toggleStrategy(strategy)}
                            className={`${
                              strategy.isActive
                                ? 'text-red-600 hover:text-red-900'
                                : 'text-green-600 hover:text-green-900'
                            }`}
                            title={strategy.isActive ? 'Pause Strategy' : 'Start Strategy'}
                          >
                            {strategy.isActive ? (
                              <PauseIcon className="h-4 w-4" />
                            ) : (
                              <PlayIcon className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleEdit(strategy)}
                            className="text-primary-600 hover:text-primary-900"
                            title="Edit Strategy"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(strategy.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Strategy"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingStrategy ? 'Edit Strategy' : 'Create New Strategy'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Strategy Name</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="My Trading Strategy"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Product</label>
                    <select
                      value={formData.productId}
                      onChange={(e) => setFormData({ ...formData, productId: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      <option value="">Select a product</option>
                      {products.map((product) => (
                        <option key={product.id} value={product.id}>
                          {product.name} ({product.symbol})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Exchange</label>
                    <select
                      value={formData.exchange}
                      onChange={(e) => setFormData({ ...formData, exchange: e.target.value as 'kraken' | 'kucoin' })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    >
                      <option value="kraken">Kraken</option>
                      <option value="kucoin">KuCoin</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Buy Amount ($)</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.buyAmount}
                      onChange={(e) => setFormData({ ...formData, buyAmount: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="100.00"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Profit Percentage (%)</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="100"
                      value={formData.profitPercentage}
                      onChange={(e) => setFormData({ ...formData, profitPercentage: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="5.00"
                      required
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        setShowModal(false);
                        setEditingStrategy(null);
                        setFormData({
                          name: '',
                          buyAmount: '',
                          profitPercentage: '',
                          exchange: 'kraken',
                          productId: '',
                        });
                      }}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"
                    >
                      {editingStrategy ? 'Update' : 'Create'} Strategy
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
