'use client';

import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { CubeIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { productsApi, subscriptionsApi } from '@/lib/api';
import { Product, Subscription } from '@/types';

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [productsData, subscriptionsData] = await Promise.all([
        productsApi.getAll(),
        subscriptionsApi.getAll(),
      ]);
      setProducts(productsData);
      setSubscriptions(subscriptionsData);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (productId: number) => {
    try {
      const response = await subscriptionsApi.create(productId);
      // Redirect to Stripe checkout
      window.location.href = response.url;
    } catch (error) {
      console.error('Failed to create subscription:', error);
    }
  };

  const isSubscribed = (productId: number) => {
    return subscriptions.some(
      (sub) => sub.productId === productId && sub.status === 'active'
    );
  };

  return (
    <DashboardLayout title="Products">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Available Products</h2>
          <p className="text-gray-600">Subscribe to crypto products to start automated trading</p>
        </div>

        {/* Products Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading products...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="bg-primary-100 p-2 rounded-lg">
                        <CubeIcon className="h-6 w-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                        <p className="text-sm text-gray-500">{product.symbol}</p>
                      </div>
                    </div>
                    {isSubscribed(product.id) && (
                      <CheckCircleIcon className="h-6 w-6 text-green-500" />
                    )}
                  </div>

                  <div className="mb-4">
                    <div className="text-2xl font-bold text-gray-900">
                      ${product.priceUsd}
                      <span className="text-sm font-normal text-gray-500">/month</span>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Features:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Automated trading strategies</li>
                      <li>• Real-time price monitoring</li>
                      <li>• Risk management tools</li>
                      <li>• Performance analytics</li>
                    </ul>
                  </div>

                  <div className="flex items-center justify-between">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        product.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {product.isActive ? 'Available' : 'Unavailable'}
                    </span>

                    {isSubscribed(product.id) ? (
                      <span className="text-green-600 font-medium">Subscribed</span>
                    ) : (
                      <button
                        onClick={() => handleSubscribe(product.id)}
                        disabled={!product.isActive}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          product.isActive
                            ? 'bg-primary-600 hover:bg-primary-700 text-white'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                      >
                        Subscribe
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Current Subscriptions */}
        {subscriptions.length > 0 && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Your Subscriptions</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expires
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {subscriptions.map((subscription) => (
                    <tr key={subscription.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="bg-primary-100 p-2 rounded-lg mr-3">
                            <CubeIcon className="h-4 w-4 text-primary-600" />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {subscription.product?.name || 'Unknown Product'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {subscription.product?.symbol || 'N/A'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            subscription.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : subscription.status === 'expired'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {subscription.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${subscription.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {subscription.expiresAt
                          ? new Date(subscription.expiresAt).toLocaleDateString()
                          : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
