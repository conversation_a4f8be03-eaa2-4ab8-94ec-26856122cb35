'use client';

import { useEffect, useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { PlusIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';
import { stepsApi, productsApi } from '@/lib/api';
import { Step, Product } from '@/types';

export default function StepsPage() {
  const [steps, setSteps] = useState<Step[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingStep, setEditingStep] = useState<Step | null>(null);
  const [formData, setFormData] = useState({
    productId: '',
    stepNumber: '',
    buyPrice: '',
    sellPrice: '',
    amount: '',
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    if (selectedProduct) {
      fetchSteps(selectedProduct);
    }
  }, [selectedProduct]);

  const fetchProducts = async () => {
    try {
      const productsData = await productsApi.getAll();
      setProducts(productsData);
      if (productsData.length > 0) {
        setSelectedProduct(productsData[0].id);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSteps = async (productId: number) => {
    try {
      const stepsData = await stepsApi.getByProduct(productId);
      setSteps(stepsData);
    } catch (error) {
      console.error('Failed to fetch steps:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const data = {
        productId: parseInt(formData.productId),
        stepNumber: parseInt(formData.stepNumber),
        buyPrice: parseFloat(formData.buyPrice),
        sellPrice: parseFloat(formData.sellPrice),
        amount: parseFloat(formData.amount),
      };

      if (editingStep) {
        await stepsApi.update(editingStep.id, data);
      } else {
        await stepsApi.create(data);
      }
      
      if (selectedProduct) {
        await fetchSteps(selectedProduct);
      }
      setShowModal(false);
      setEditingStep(null);
      setFormData({
        productId: '',
        stepNumber: '',
        buyPrice: '',
        sellPrice: '',
        amount: '',
      });
    } catch (error) {
      console.error('Failed to save step:', error);
    }
  };

  const handleEdit = (step: Step) => {
    setEditingStep(step);
    setFormData({
      productId: step.productId.toString(),
      stepNumber: step.stepNumber.toString(),
      buyPrice: step.buyPrice.toString(),
      sellPrice: step.sellPrice.toString(),
      amount: step.amount.toString(),
    });
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (confirm('Are you sure you want to delete this step?')) {
      try {
        await stepsApi.delete(id);
        if (selectedProduct) {
          await fetchSteps(selectedProduct);
        }
      } catch (error) {
        console.error('Failed to delete step:', error);
      }
    }
  };

  const handleAddStep = () => {
    setFormData({
      productId: selectedProduct?.toString() || '',
      stepNumber: (steps.length + 1).toString(),
      buyPrice: '',
      sellPrice: '',
      amount: '',
    });
    setShowModal(true);
  };

  return (
    <DashboardLayout title="Trading Steps">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Trading Steps</h2>
            <p className="text-gray-600">Configure step-by-step trading rules for each product</p>
          </div>
          <button
            onClick={handleAddStep}
            disabled={!selectedProduct}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Add Step</span>
          </button>
        </div>

        {/* Product Selector */}
        <div className="bg-white shadow rounded-lg p-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Product</label>
          <select
            value={selectedProduct || ''}
            onChange={(e) => setSelectedProduct(parseInt(e.target.value))}
            className="block w-full max-w-xs border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">Choose a product</option>
            {products.map((product) => (
              <option key={product.id} value={product.id}>
                {product.name} ({product.symbol})
              </option>
            ))}
          </select>
        </div>

        {/* Steps List */}
        {selectedProduct && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Trading Steps for {products.find(p => p.id === selectedProduct)?.name}
              </h3>
            </div>
            {loading ? (
              <div className="p-6 text-center">Loading...</div>
            ) : steps.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No steps configured for this product. Add your first step to get started.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Step #
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Buy Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sell Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Profit
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {steps
                      .sort((a, b) => a.stepNumber - b.stepNumber)
                      .map((step) => {
                        const profit = ((step.sellPrice - step.buyPrice) / step.buyPrice) * 100;
                        return (
                          <tr key={step.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {step.stepNumber}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ${step.buyPrice.toFixed(8)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ${step.sellPrice.toFixed(8)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {step.amount.toFixed(8)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span className={profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                                {profit.toFixed(2)}%
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(step.createdAt).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleEdit(step)}
                                className="text-primary-600 hover:text-primary-900 mr-3"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(step.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingStep ? 'Edit Step' : 'Add New Step'}
                </h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Step Number</label>
                    <input
                      type="number"
                      min="1"
                      value={formData.stepNumber}
                      onChange={(e) => setFormData({ ...formData, stepNumber: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Buy Price</label>
                    <input
                      type="number"
                      step="0.00000001"
                      min="0"
                      value={formData.buyPrice}
                      onChange={(e) => setFormData({ ...formData, buyPrice: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="0.00000000"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Sell Price</label>
                    <input
                      type="number"
                      step="0.00000001"
                      min="0"
                      value={formData.sellPrice}
                      onChange={(e) => setFormData({ ...formData, sellPrice: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="0.00000000"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Amount</label>
                    <input
                      type="number"
                      step="0.00000001"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="0.00000000"
                      required
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        setShowModal(false);
                        setEditingStep(null);
                        setFormData({
                          productId: '',
                          stepNumber: '',
                          buyPrice: '',
                          sellPrice: '',
                          amount: '',
                        });
                      }}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"
                    >
                      {editingStep ? 'Update' : 'Add'} Step
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
