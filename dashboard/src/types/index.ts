export interface User {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  timezone?: string;
  isVerified: boolean;
  role: "user" | "moderator" | "admin";
  verificationCode?: string;
  verificationExpires?: Date;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

export interface Product {
  id: number;
  symbol: string;
  name: string;
  priceUsd: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ApiKey {
  id: number;
  exchange: "kraken" | "kucoin";
  keyName: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Strategy {
  id: number;
  name: string;
  buyAmount: number;
  profitPercentage: number;
  exchange: "kraken" | "kucoin";
  productId: number;
  product?: Product;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Step {
  id: number;
  productId: number;
  product?: Product;
  stepNumber: number;
  buyPrice: number;
  sellPrice: number;
  amount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: number;
  strategyId: number;
  strategy?: Strategy;
  symbol: string;
  amount: number;
  price: number;
  time: string;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

export interface Subscription {
  id: number;
  userId: number;
  productId: number;
  product?: Product;
  stripeId?: string;
  status: "active" | "expired" | "cancelled";
  amount: number;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}
