/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/strategies/page";
exports.ids = ["app/dashboard/strategies/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstrategies%2Fpage&page=%2Fdashboard%2Fstrategies%2Fpage&appPaths=%2Fdashboard%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstrategies%2Fpage.tsx&appDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstrategies%2Fpage&page=%2Fdashboard%2Fstrategies%2Fpage&appPaths=%2Fdashboard%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstrategies%2Fpage.tsx&appDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?6a4d\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_app_render_strip_flight_headers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/app-render/strip-flight-headers */ \"(rsc)/./node_modules/next/dist/server/app-render/strip-flight-headers.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/strategies/page.tsx */ \"(rsc)/./src/app/dashboard/strategies/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'strategies',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/strategies/page\",\n        pathname: \"/dashboard/strategies\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/dashboard/strategies/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig, interceptionRoutePatterns } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = routeModule.match(pathname, prerenderManifest);\n    const isPrerendered = !!prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_ROUTER_PREFETCH_HEADER] === '1' // exclude runtime prefetches, which use '2'\n    ;\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (!routeModule.isDev && !isDraftMode && isSSG && isRSCRequest && !isDynamicRSCRequest) {\n        (0,next_dist_server_app_render_strip_flight_headers__WEBPACK_IMPORTED_MODULE_7__.stripFlightHeaders)(req.headers);\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const varyHeader = routeModule.getVaryHeader(resolvedPathname, interceptionRoutePatterns);\n        res.setHeader('Vary', varyHeader);\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.cacheComponents && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir:  true ? (__webpack_require__(/*! path */ \"path\").join)(/* turbopackIgnore: true */ process.cwd(), routeModule.relativeProjectDir) : 0,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        clientParamParsing: Boolean(nextConfig.experimental.clientParamParsing),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a HTML bot request, we want to serve a blocking render and\n            // not the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__.isBot)(userAgent)) {\n                if (!isRoutePPREnabled || isHtmlBot) {\n                    fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER;\n                }\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    const cacheKey = typeof (prerenderInfo == null ? void 0 : prerenderInfo.fallback) === 'string' ? prerenderInfo.fallback : isProduction ? normalizedSrcPage : null;\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].EMPTY,\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].fromStatic(matchedSegment, next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_CONTENT_TYPE_HEADER),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].EMPTY,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].fromStatic(cachedData.rscData, next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_CONTENT_TYPE_HEADER),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode || isRSCRequest) {\n                // If we're in test mode, we should add a sentinel chunk to the response\n                // that's between the static and dynamic parts so we can compare the\n                // chunks and add assertions.\n                if (false) {}\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.push(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_22__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // If we're in test mode, we should add a sentinel chunk to the response\n            // that's between the static and dynamic parts so we can compare the\n            // chunks and add assertions.\n            if (false) {}\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.push(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                req,\n                res,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n// TODO: omit this from production builds, only test builds should include it\n/**\n * Creates a readable stream that emits a PPR boundary sentinel.\n *\n * @returns A readable stream that emits a PPR boundary sentinel.\n */ function createPPRBoundarySentinel() {\n    return new ReadableStream({\n        start (controller) {\n            controller.enqueue(new TextEncoder().encode('<!-- PPR_BOUNDARY_SENTINEL -->'));\n            controller.close();\n        }\n    });\n}\n\n//# sourceMappingURL=app-page.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstrategies%2Fpage&page=%2Fdashboard%2Fstrategies%2Fpage&appPaths=%2Fdashboard%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstrategies%2Fpage.tsx&appDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/framework/boundary-components.js */ \"(rsc)/./node_modules/next/dist/lib/framework/boundary-components.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJlZiUyRkRhdGElMkZQcm9qZWN0cyUyRjIwMjUlMkZQaW5nUG9uZyUyRmRhc2hib2FyZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFyZWYlMkZEYXRhJTJGUHJvamVjdHMlMkYyMDI1JTJGUGluZ1BvbmclMkZkYXNoYm9hcmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFyZWYlMkZEYXRhJTJGUHJvamVjdHMlMkYyMDI1JTJGUGluZ1BvbmclMkZkYXNoYm9hcmQlMkZzcmMlMkZjb250ZXh0cyUyRkF1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/strategies/page.tsx */ \"(rsc)/./src/app/dashboard/strategies/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJlZiUyRkRhdGElMkZQcm9qZWN0cyUyRjIwMjUlMkZQaW5nUG9uZyUyRmRhc2hib2FyZCUyRnNyYyUyRmFwcCUyRmRhc2hib2FyZCUyRnN0cmF0ZWdpZXMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQTRIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9hcHAvZGFzaGJvYXJkL3N0cmF0ZWdpZXMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/strategies/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/strategies/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js\");\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);\n// This file is generated by the Webpack next-flight-loader.\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rhc2hib2FyZC9zdHJhdGVnaWVzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBIiwic291cmNlcyI6WyJfTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvc3RyYXRlZ2llcy9wYWdlLnRzeC9fX25leHRqcy1pbnRlcm5hbC1wcm94eS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBmaWxlIGlzIGdlbmVyYXRlZCBieSB0aGUgV2VicGFjayBuZXh0LWZsaWdodC1sb2FkZXIuXG5pbXBvcnQgeyByZWdpc3RlckNsaWVudFJlZmVyZW5jZSB9IGZyb20gXCJyZWFjdC1zZXJ2ZXItZG9tLXdlYnBhY2svc2VydmVyXCI7XG5leHBvcnQgZGVmYXVsdCByZWdpc3RlckNsaWVudFJlZmVyZW5jZShcbmZ1bmN0aW9uKCkgeyB0aHJvdyBuZXcgRXJyb3IoXCJBdHRlbXB0ZWQgdG8gY2FsbCB0aGUgZGVmYXVsdCBleHBvcnQgb2YgXFxcIi9Vc2Vycy9hcmVmL0RhdGEvUHJvamVjdHMvMjAyNS9QaW5nUG9uZy9kYXNoYm9hcmQvc3JjL2FwcC9kYXNoYm9hcmQvc3RyYXRlZ2llcy9wYWdlLnRzeFxcXCIgZnJvbSB0aGUgc2VydmVyLCBidXQgaXQncyBvbiB0aGUgY2xpZW50LiBJdCdzIG5vdCBwb3NzaWJsZSB0byBpbnZva2UgYSBjbGllbnQgZnVuY3Rpb24gZnJvbSB0aGUgc2VydmVyLCBpdCBjYW4gb25seSBiZSByZW5kZXJlZCBhcyBhIENvbXBvbmVudCBvciBwYXNzZWQgdG8gcHJvcHMgb2YgYSBDbGllbnQgQ29tcG9uZW50LlwiKTsgfSxcblwiL1VzZXJzL2FyZWYvRGF0YS9Qcm9qZWN0cy8yMDI1L1BpbmdQb25nL2Rhc2hib2FyZC9zcmMvYXBwL2Rhc2hib2FyZC9zdHJhdGVnaWVzL3BhZ2UudHN4XCIsXG5cImRlZmF1bHRcIixcbik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/strategies/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"da583c99ff32\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYTU4M2M5OWZmMzJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'PingPong Dashboard',\n    description: 'Crypto Auto-Trading Platform Dashboard'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQytCO0FBSS9DLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsK0RBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1BpbmdQb25nIERhc2hib2FyZCcsXG4gIGRlc2NyaXB0aW9uOiAnQ3J5cHRvIEF1dG8tVHJhZGluZyBQbGF0Zm9ybSBEYXNoYm9hcmQnLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js\");\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);\n// This file is generated by the Webpack next-flight-loader.\n\nconst useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/contexts/AuthContext.tsx\",\n\"useAuth\",\n);const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/contexts/AuthContext.tsx\",\n\"AuthProvider\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSIsInNvdXJjZXMiOlsiX05fRS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3gvX19uZXh0anMtaW50ZXJuYWwtcHJveHkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgZmlsZSBpcyBnZW5lcmF0ZWQgYnkgdGhlIFdlYnBhY2sgbmV4dC1mbGlnaHQtbG9hZGVyLlxuaW1wb3J0IHsgcmVnaXN0ZXJDbGllbnRSZWZlcmVuY2UgfSBmcm9tIFwicmVhY3Qtc2VydmVyLWRvbS13ZWJwYWNrL3NlcnZlclwiO1xuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSByZWdpc3RlckNsaWVudFJlZmVyZW5jZShcbmZ1bmN0aW9uKCkgeyB0aHJvdyBuZXcgRXJyb3IoXCJBdHRlbXB0ZWQgdG8gY2FsbCB1c2VBdXRoKCkgZnJvbSB0aGUgc2VydmVyIGJ1dCB1c2VBdXRoIGlzIG9uIHRoZSBjbGllbnQuIEl0J3Mgbm90IHBvc3NpYmxlIHRvIGludm9rZSBhIGNsaWVudCBmdW5jdGlvbiBmcm9tIHRoZSBzZXJ2ZXIsIGl0IGNhbiBvbmx5IGJlIHJlbmRlcmVkIGFzIGEgQ29tcG9uZW50IG9yIHBhc3NlZCB0byBwcm9wcyBvZiBhIENsaWVudCBDb21wb25lbnQuXCIpOyB9LFxuXCIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3hcIixcblwidXNlQXV0aFwiLFxuKTtleHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyID0gcmVnaXN0ZXJDbGllbnRSZWZlcmVuY2UoXG5mdW5jdGlvbigpIHsgdGhyb3cgbmV3IEVycm9yKFwiQXR0ZW1wdGVkIHRvIGNhbGwgQXV0aFByb3ZpZGVyKCkgZnJvbSB0aGUgc2VydmVyIGJ1dCBBdXRoUHJvdmlkZXIgaXMgb24gdGhlIGNsaWVudC4gSXQncyBub3QgcG9zc2libGUgdG8gaW52b2tlIGEgY2xpZW50IGZ1bmN0aW9uIGZyb20gdGhlIHNlcnZlciwgaXQgY2FuIG9ubHkgYmUgcmVuZGVyZWQgYXMgYSBDb21wb25lbnQgb3IgcGFzc2VkIHRvIHByb3BzIG9mIGEgQ2xpZW50IENvbXBvbmVudC5cIik7IH0sXG5cIi9Vc2Vycy9hcmVmL0RhdGEvUHJvamVjdHMvMjAyNS9QaW5nUG9uZy9kYXNoYm9hcmQvc3JjL2NvbnRleHRzL0F1dGhDb250ZXh0LnRzeFwiLFxuXCJBdXRoUHJvdmlkZXJcIixcbik7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/framework/boundary-components.js */ \"(ssr)/./node_modules/next/dist/lib/framework/boundary-components.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJlZiUyRkRhdGElMkZQcm9qZWN0cyUyRjIwMjUlMkZQaW5nUG9uZyUyRmRhc2hib2FyZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFyZWYlMkZEYXRhJTJGUHJvamVjdHMlMkYyMDI1JTJGUGluZ1BvbmclMkZkYXNoYm9hcmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFyZWYlMkZEYXRhJTJGUHJvamVjdHMlMkYyMDI1JTJGUGluZ1BvbmclMkZkYXNoYm9hcmQlMkZzcmMlMkZjb250ZXh0cyUyRkF1dGhDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/strategies/page.tsx */ \"(ssr)/./src/app/dashboard/strategies/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJlZiUyRkRhdGElMkZQcm9qZWN0cyUyRjIwMjUlMkZQaW5nUG9uZyUyRmRhc2hib2FyZCUyRnNyYyUyRmFwcCUyRmRhc2hib2FyZCUyRnN0cmF0ZWdpZXMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQTRIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJlZi9EYXRhL1Byb2plY3RzLzIwMjUvUGluZ1BvbmcvZGFzaGJvYXJkL3NyYy9hcHAvZGFzaGJvYXJkL3N0cmF0ZWdpZXMvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Fstrategies%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/strategies/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/strategies/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StrategiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PauseIcon,PencilIcon,PlayIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PauseIcon,PencilIcon,PlayIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PauseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=PauseIcon,PencilIcon,PlayIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=PauseIcon,PencilIcon,PlayIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=PauseIcon,PencilIcon,PlayIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction StrategiesPage() {\n    const [strategies, setStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showModal, setShowModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStrategy, setEditingStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        buyAmount: '',\n        profitPercentage: '',\n        exchange: 'kraken',\n        productId: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StrategiesPage.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"StrategiesPage.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            const [strategiesData, productsData] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.strategiesApi.getAll(),\n                _lib_api__WEBPACK_IMPORTED_MODULE_3__.productsApi.getAll()\n            ]);\n            setStrategies(strategiesData);\n            setProducts(productsData);\n        } catch (error) {\n            console.error('Failed to fetch data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const data = {\n                ...formData,\n                buyAmount: parseFloat(formData.buyAmount),\n                profitPercentage: parseFloat(formData.profitPercentage),\n                productId: parseInt(formData.productId)\n            };\n            if (editingStrategy) {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_3__.strategiesApi.update(editingStrategy.id, data);\n            } else {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_3__.strategiesApi.create(data);\n            }\n            await fetchData();\n            setShowModal(false);\n            setEditingStrategy(null);\n            setFormData({\n                name: '',\n                buyAmount: '',\n                profitPercentage: '',\n                exchange: 'kraken',\n                productId: ''\n            });\n        } catch (error) {\n            console.error('Failed to save strategy:', error);\n        }\n    };\n    const handleEdit = (strategy)=>{\n        setEditingStrategy(strategy);\n        setFormData({\n            name: strategy.name,\n            buyAmount: strategy.buyAmount.toString(),\n            profitPercentage: strategy.profitPercentage.toString(),\n            exchange: strategy.exchange,\n            productId: strategy.productId.toString()\n        });\n        setShowModal(true);\n    };\n    const handleDelete = async (id)=>{\n        if (confirm('Are you sure you want to delete this strategy?')) {\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_3__.strategiesApi.delete(id);\n                await fetchData();\n            } catch (error) {\n                console.error('Failed to delete strategy:', error);\n            }\n        }\n    };\n    const toggleStrategy = async (strategy)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__.strategiesApi.update(strategy.id, {\n                isActive: !strategy.isActive\n            });\n            await fetchData();\n        } catch (error) {\n            console.error('Failed to toggle strategy:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"Strategies\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Trading Strategies\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Create and manage your automated trading strategies\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowModal(true),\n                            className: \"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Create Strategy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this) : strategies.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center text-gray-500\",\n                        children: \"No strategies found. Create your first strategy to get started.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Strategy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Exchange\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Buy Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Profit %\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: strategies.map((strategy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: strategy.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Created \",\n                                                                new Date(strategy.createdAt).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: strategy.product?.name || 'Unknown'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: strategy.product?.symbol || 'N/A'\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize\",\n                                                    children: strategy.exchange\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                    children: [\n                                                        \"$\",\n                                                        strategy.buyAmount\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                                    children: [\n                                                        strategy.profitPercentage,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${strategy.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                                                        children: strategy.isActive ? 'Active' : 'Inactive'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleStrategy(strategy),\n                                                                className: `${strategy.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`,\n                                                                title: strategy.isActive ? 'Pause Strategy' : 'Start Strategy',\n                                                                children: strategy.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEdit(strategy),\n                                                                className: \"text-primary-600 hover:text-primary-900\",\n                                                                title: \"Edit Strategy\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(strategy.id),\n                                                                className: \"text-red-600 hover:text-red-900\",\n                                                                title: \"Delete Strategy\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PauseIcon_PencilIcon_PlayIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, strategy.id, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: editingStrategy ? 'Edit Strategy' : 'Create New Strategy'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Strategy Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                                                    placeholder: \"My Trading Strategy\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Product\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.productId,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            productId: e.target.value\n                                                        }),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                                                    required: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: product.id,\n                                                                children: [\n                                                                    product.name,\n                                                                    \" (\",\n                                                                    product.symbol,\n                                                                    \")\"\n                                                                ]\n                                                            }, product.id, true, {\n                                                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Exchange\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.exchange,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            exchange: e.target.value\n                                                        }),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                                                    required: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"kraken\",\n                                                            children: \"Kraken\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"kucoin\",\n                                                            children: \"KuCoin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Buy Amount ($)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    min: \"0\",\n                                                    value: formData.buyAmount,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            buyAmount: e.target.value\n                                                        }),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                                                    placeholder: \"100.00\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700\",\n                                                    children: \"Profit Percentage (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    min: \"0.01\",\n                                                    max: \"100\",\n                                                    value: formData.profitPercentage,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            profitPercentage: e.target.value\n                                                        }),\n                                                    className: \"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500\",\n                                                    placeholder: \"5.00\",\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end space-x-3 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        setShowModal(false);\n                                                        setEditingStrategy(null);\n                                                        setFormData({\n                                                            name: '',\n                                                            buyAmount: '',\n                                                            profitPercentage: '',\n                                                            exchange: 'kraken',\n                                                            productId: ''\n                                                        });\n                                                    },\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md\",\n                                                    children: [\n                                                        editingStrategy ? 'Update' : 'Create',\n                                                        \" Strategy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/app/dashboard/strategies/page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/strategies/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardLayout({ children, title }) {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        title: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/DashboardLayout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Header({ title }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-semibold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Header.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUV1RDtBQU14QyxTQUFTQyxPQUFPLEVBQUVDLEtBQUssRUFBZTtJQUNuRCxxQkFDRSw4REFBQ0M7UUFBT0MsV0FBVTtrQkFDaEIsNEVBQUNDO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNFOzRCQUFHRixXQUFVO3NDQUF3Q0Y7Ozs7Ozs7Ozs7O2tDQUd4RCw4REFBQ0c7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNHOzRCQUFPSCxXQUFVO3NDQUNoQiw0RUFBQ0osa0dBQVFBO2dDQUFDSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sQyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyZWYvRGF0YS9Qcm9qZWN0cy8yMDI1L1BpbmdQb25nL2Rhc2hib2FyZC9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IEJlbGxJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIEhlYWRlclByb3BzIHtcbiAgdGl0bGU6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVhZGVyKHsgdGl0bGUgfTogSGVhZGVyUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvd1wiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGgtMTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3RpdGxlfTwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0xIHJvdW5kZWQtZnVsbCB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS01MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDBcIj5cbiAgICAgICAgICAgICAgPEJlbGxJY29uIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9oZWFkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQmVsbEljb24iLCJIZWFkZXIiLCJ0aXRsZSIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImgxIiwiYnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,ChartBarIcon,ClockIcon,CubeIcon,HomeIcon,KeyIcon,ListBulletIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: 'API Keys',\n        href: '/dashboard/api-keys',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Products',\n        href: '/dashboard/products',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Strategies',\n        href: '/dashboard/strategies',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Steps',\n        href: '/dashboard/steps',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Orders',\n        href: '/dashboard/orders',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Profile',\n        href: '/dashboard/profile',\n        icon: _barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col w-64 bg-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 px-4 bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-white\",\n                    children: \"PingPong\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-2 py-4 space-y-1\",\n                        children: navigation.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: `\n                  group flex items-center px-2 py-2 text-sm font-medium rounded-md\n                  ${isActive ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}\n                `,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: `\n                    mr-3 h-6 w-6 flex-shrink-0\n                    ${isActive ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300'}\n                  `,\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, item.name, true, {\n                                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 p-4 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-white truncate\",\n                                            children: user?.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 capitalize\",\n                                            children: user?.role\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    className: \"ml-3 flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_ChartBarIcon_ClockIcon_CubeIcon_HomeIcon_KeyIcon_ListBulletIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/components/layout/Sidebar.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const login = async (email, password)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.login(email, password);\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set('token', response.access_token, {\n                expires: 7\n            }); // 7 days\n            setUser(response.user);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove('token');\n        setUser(null);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('token');\n            if (token) {\n                const userData = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.authApi.getProfile();\n                setUser(userData);\n            }\n        } catch (error) {\n            console.error('Failed to refresh user:', error);\n            logout();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    const token = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get('token');\n                    if (token) {\n                        await refreshUser();\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/Projects/2025/PingPong/dashboard/src/contexts/AuthContext.tsx\",\n        lineNumber: 78,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiKeysApi: () => (/* binding */ apiKeysApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ordersApi: () => (/* binding */ ordersApi),\n/* harmony export */   productsApi: () => (/* binding */ productsApi),\n/* harmony export */   stepsApi: () => (/* binding */ stepsApi),\n/* harmony export */   strategiesApi: () => (/* binding */ strategiesApi),\n/* harmony export */   subscriptionsApi: () => (/* binding */ subscriptionsApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n});\n// Response interceptor to handle auth errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401) {\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"token\");\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// Auth API\nconst authApi = {\n    login: async (email, password)=>{\n        const response = await api.post(\"/auth/login\", {\n            email,\n            password\n        });\n        return response.data.data; // Extract data from wrapped response\n    },\n    register: async (email, password)=>{\n        const response = await api.post(\"/auth/register\", {\n            email,\n            password\n        });\n        return response.data.data; // Extract data from wrapped response\n    },\n    verifyEmail: async (email, code)=>{\n        const response = await api.post(\"/auth/verify-email\", {\n            email,\n            code\n        });\n        return response.data.data; // Extract data from wrapped response\n    },\n    resendVerification: async (email)=>{\n        const response = await api.post(\"/auth/resend-verification\", {\n            email\n        });\n        return response.data.data; // Extract data from wrapped response\n    },\n    getProfile: async ()=>{\n        const response = await api.get(\"/auth/profile\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    updateProfile: async (data)=>{\n        const response = await api.put(\"/auth/profile\", data);\n        return response.data.data; // Extract data from wrapped response\n    }\n};\n// Products API\nconst productsApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/products\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/products/${id}`);\n        return response.data.data; // Extract data from wrapped response\n    }\n};\n// API Keys API\nconst apiKeysApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/api-keys\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    create: async (data)=>{\n        const response = await api.post(\"/api-keys\", data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    update: async (id, data)=>{\n        const response = await api.patch(`/api-keys/${id}`, data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    delete: async (id)=>{\n        await api.delete(`/api-keys/${id}`);\n    // Delete operations typically don't return data\n    }\n};\n// Strategies API\nconst strategiesApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/strategies\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    create: async (data)=>{\n        const response = await api.post(\"/strategies\", data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    update: async (id, data)=>{\n        const response = await api.patch(`/strategies/${id}`, data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    delete: async (id)=>{\n        await api.delete(`/strategies/${id}`);\n    // Delete operations typically don't return data\n    }\n};\n// Steps API\nconst stepsApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/steps\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    getByProduct: async (productId)=>{\n        const response = await api.get(`/steps/product/${productId}`);\n        return response.data.data; // Extract data from wrapped response\n    },\n    create: async (data)=>{\n        const response = await api.post(\"/steps\", data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    update: async (id, data)=>{\n        const response = await api.patch(`/steps/${id}`, data);\n        return response.data.data; // Extract data from wrapped response\n    },\n    delete: async (id)=>{\n        await api.delete(`/steps/${id}`);\n    // Delete operations typically don't return data\n    }\n};\n// Orders API\nconst ordersApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/orders\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    getByStrategy: async (strategyId)=>{\n        const response = await api.get(`/orders/strategy/${strategyId}`);\n        return response.data.data; // Extract data from wrapped response\n    }\n};\n// Subscriptions API\nconst subscriptionsApi = {\n    getAll: async ()=>{\n        const response = await api.get(\"/subscriptions\");\n        return response.data.data; // Extract data from wrapped response\n    },\n    create: async (productId)=>{\n        const response = await api.post(\"/subscriptions\", {\n            productId\n        });\n        return response.data.data; // Extract data from wrapped response\n    },\n    cancel: async (id)=>{\n        await api.delete(`/subscriptions/${id}`);\n    // Delete operations typically don't return data\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/@swc","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fstrategies%2Fpage&page=%2Fdashboard%2Fstrategies%2Fpage&appPaths=%2Fdashboard%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fstrategies%2Fpage.tsx&appDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faref%2FData%2FProjects%2F2025%2FPingPong%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();