// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}


// Validate ../../src/app/dashboard/api-keys/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/api-keys">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/api-keys/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/orders/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/orders">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/orders/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/products/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/products">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/products/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/profile/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/profile">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/profile/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/steps/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/steps">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/steps/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/dashboard/strategies/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/dashboard/strategies">> = Specific
  const handler = {} as typeof import("../../src/app/dashboard/strategies/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/login/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/login">> = Specific
  const handler = {} as typeof import("../../src/app/login/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/register/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/register">> = Specific
  const handler = {} as typeof import("../../src/app/register/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}







// Validate ../../src/app/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/">> = Specific
  const handler = {} as typeof import("../../src/app/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
