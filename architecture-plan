# Crypto Auto-Trading Platform - Backend Architecture Plan

## 1. Overview

This document outlines the backend architecture, user flow, database schema, and system components required to build a scalable, testable auto-trading platform for crypto. The backend will be built with NestJS, PostgreSQL, Redis, and integrated with Stripe, Kraken, and KuCoin APIs.

---

## 2. User Flow

### Step-by-step:

1. **User Registration**

   * Sign up with email/password
   * Email verification (code sent via email)

2. **User Login**

   * JWT-based auth

3. **View Products**

   * List of available coins (BTC, SOL, etc.) with subscription prices

4. **Subscription Purchase**

   * Stripe payment integration (future crypto payment support)
   * Upon success, activate subscription with expiration date

5. **API Key Submission**

   * User adds exchange (Kraken/KuCoin) API credentials
   * Encrypted storage

6. **Price Streaming & Trade Execution**

   * Kraken WebSocket sends price updates
   * For each update:

     * Identify users subscribed to that coin
     * For each user:

       * Fetch balance & open orders from exchange API
       * Check strategy steps (predefined)
       * Decide: Buy / Sell / Skip
       * Store order data in DB for tracking/analysis

---

## 3. System Architecture

### Tech Stack

* **NestJS** – Backend framework
* **PostgreSQL** – Primary DB
* **Redis + BullMQ** – Job queues
* **Stripe** – Subscription payment
* **WebSockets** – Kraken market data
* **Docker** – Containerized services
* **Jest** – Testing framework

### Services

```
[Client App]
   |
[API Gateway (NestJS)]
   ├── AuthService
   ├── EmailService
   ├── SubscriptionService
   ├── ProductService
   ├── ExchangeService
   ├── PriceStreamService
   ├── StrategyService
   ├── OrderService
   └── QueueService (BullMQ)

[KrakenSocketWorker] ──> [Redis PubSub]
                          └── [UserStrategyExecutor (Worker)]
```

---

## 4. Database Schema

### users

* id
* email
* hashed\_password
* is\_verified
* created\_at

### verification\_codes

* id
* user\_id
* code
* expires\_at

### products

* id
* coin\_name (e.g., BTC)
* price\_usd

### subscriptions

* id
* user\_id
* product\_id
* stripe\_id
* status (active/expired)
* expires\_at

### exchange\_accounts

* id
* user\_id
* exchange\_name
* api\_key (encrypted)
* api\_secret (encrypted)
* max\_trade\_amount

### user\_pairs

* id
* user\_id
* coin\_symbol
* is\_active

### strategy\_steps

* id
* pair\_id
* step\_number
* price\_trigger
* action (buy/sell/skip)

### trades

* id
* user\_id
* pair
* action
* amount
* price
* timestamp

---

## 5. Services and Logic

### EmailService

* Generates and sends email verification codes

### SubscriptionService

* Integrates Stripe Checkout
* Listens to webhooks to activate subscriptions

### PriceStreamService

* Maintains single Kraken WebSocket connection
* Publishes updates to Redis

### QueueService

* Subscribes to price updates
* Finds all users subscribed to that pair
* Enqueues job per user to evaluate strategy

### StrategyService

* Loads user’s exchange data
* Fetches balance & open/closed orders
* Runs decision engine based on strategy\_steps
* Calls ExchangeService to execute trade if needed

### OrderService

* Stores every executed trade
* Links them to strategy steps
* Used for analytics later

---

## 6. Step-by-Step Development Plan

### Phase 1: Project Setup

* [x] Setup NestJS project with PostgreSQL and Redis
* [x] Configure TypeORM or Prisma with PostgreSQL
* [x] Setup environment-based config (dotenv)
* [x] Dockerize the project (NestJS, Redis, Postgres)
* [x] Add global error handling and logging (Winston)

### Phase 2: Subscriptions & Payments

* [x] Create `products` module to define coin subscriptions
* [x] Integrate Stripe Checkout for payments
* [x] Create Stripe webhook to mark subscriptions active
* [ ] Add expiration logic with cron/worker

### Phase 3: Exchange Integration

* [ ] Create `exchange_accounts` module
* [ ] Add encryption/decryption for sensitive keys
* [ ] Create Kraken and KuCoin service wrappers
* [ ] Write balance & order fetchers using exchange APIs

### Phase 4: Price Streaming

* [ ] Connect to Kraken WebSocket
* [ ] Subscribe to ticker channels
* [ ] Broadcast updates to Redis Pub/Sub

### Phase 5: Strategy Execution

* [ ] Subscribe to price updates from Redis
* [ ] Match users subscribed to each symbol
* [ ] Fetch user balances & open orders in real-time
* [ ] Run strategy evaluation logic
* [ ] Place order if needed and log it

### Phase 6: Order Storage & Analytics

* [ ] Create order service to log trades with status
* [ ] Link trade records to strategy steps
* [ ] Add user dashboard endpoint to retrieve trade history

### Phase 7: Testing & Deployment

* [ ] Add unit tests (Jest) for each module
* [ ] Write integration tests using Supertest
* [ ] Add CI workflow for tests & linting
* [ ] Deploy with Docker to Railway or VPS

---

Let me know when you're ready to start the first module (Auth + Email Verification).
