import { MigrationInterface, QueryRunner } from "typeorm";

export class Update1747760602184 implements MigrationInterface {
    name = 'Update1747760602184'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "price"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "time"`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "buyPrice" numeric(20,8) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "sellPrice" numeric(20,8) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "sellPrice"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "buyPrice"`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "time" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "price" numeric(20,8) NOT NULL`);
    }

}
