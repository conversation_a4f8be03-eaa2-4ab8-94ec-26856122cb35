import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateData1747361911309 implements MigrationInterface {
    name = 'UpdateData1747361911309'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "orders" ("id" SERIAL NOT NULL, "strategyId" integer NOT NULL, "symbol" character varying NOT NULL, "amount" numeric(20,8) NOT NULL, "price" numeric(20,8) NOT NULL, "time" TIMESTAMP NOT NULL, "userId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD "amount" numeric(20,8) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP COLUMN "amount"`);
        await queryRunner.query(`DROP TABLE "orders"`);
    }

}
