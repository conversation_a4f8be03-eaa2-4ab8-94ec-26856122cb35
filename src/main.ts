import { HttpException, HttpStatus, ValidationPipe } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import * as express from "express";
import { NextFunction, Request, Response } from "express";
import { AppModule } from "./app.module";
import { HttpExceptionFilter } from "./common/filters/http-exception.filter";
import { TransformInterceptor } from "./common/interceptors/transform.interceptor";
import { createLogger } from "./common/logger/logger.factory";
import { initializeDatabase } from "./config/database.init";

async function bootstrap() {
  const logger = createLogger("PingPong");

  // Initialize database and run migrations if needed
  try {
    await initializeDatabase();
    logger.log("Database initialization completed");
  } catch (error) {
    logger.error("Failed to initialize database:", error);
    process.exit(1);
  }

  const app = await NestFactory.create(AppModule, {
    logger,
  });

  const configService = app.get(ConfigService);

  // Add express.json() middleware to parse request bodies
  app.use(express.json());

  // Request logging middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    const logData = JSON.stringify(
      {
        method: req.method,
        url: req.url,
        body: req.body,
        query: req.query,
        params: req.params,
        headers: {
          ...req.headers,
          authorization: req.headers.authorization ? "[REDACTED]" : undefined,
        },
      },
      null,
      2,
    );

    logger.log(`Incoming request: ${req.method} ${req.url}\n${logData}`);

    // Add response logging with error details
    const originalSend = res.send;
    res.send = function (body) {
      const responseBody = typeof body === "string" ? JSON.parse(body) : body;
      const responseLog = JSON.stringify(
        {
          statusCode: res.statusCode,
          body: responseBody,
          error: res.locals.error,
        },
        null,
        2,
      );
      logger.log(`Outgoing response: ${req.method} ${req.url}\n${responseLog}`);
      return originalSend.call(this, body);
    };

    next();
  });

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      validationError: {
        target: false,
        value: true,
      },
      exceptionFactory: errors => {
        const formattedErrors = errors.map(error => ({
          field: error.property,
          messages: Object.values(error.constraints || {}),
          value: error.value,
          type: "validation_error",
        }));

        const missingFields = formattedErrors
          .filter(error =>
            error.messages.some(
              msg =>
                msg.includes("should not be empty") ||
                msg.includes("must be a string") ||
                msg.includes("must be a valid enum value"),
            ),
          )
          .map(error => error.field);

        throw new HttpException(
          {
            status: "error",
            data: null,
            error: {
              code: "VALIDATION_ERROR",
              message: "Validation failed",
              details: {
                errors: formattedErrors,
                missingFields: missingFields.length > 0 ? missingFields : undefined,
              },
            },
          },
          HttpStatus.BAD_REQUEST,
        );
      },
    }),
  );

  // Global filters
  app.useGlobalFilters(new HttpExceptionFilter());

  // Global interceptors
  app.useGlobalInterceptors(new TransformInterceptor());

  // Enable CORS
  app.enableCors();

  // Set global prefix
  app.setGlobalPrefix("api");

  const port = configService.get<number>("PORT") || 3000;
  await app.listen(port);
  logger.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();
