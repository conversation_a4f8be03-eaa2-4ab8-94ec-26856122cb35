import { Body, Controller, Delete, Get, Param, Post, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Throttle } from "@nestjs/throttler";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ExchangesService } from "./exchanges.service";

@ApiTags("exchanges")
@Controller("exchanges")
@UseGuards(JwtAuthGuard)
@Throttle({ default: { limit: 30, ttl: 60 } })
export class ExchangesController {
  constructor(private readonly exchangesService: ExchangesService) {}

  @Get("ticker/:symbol")
  @ApiOperation({ summary: "Get ticker information for a symbol" })
  @ApiResponse({ status: 200, description: "Returns ticker information" })
  async getTicker(
    @Param("symbol") symbol: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getTicker(symbol, apiKey, apiSecret, exchange);
  }

  @Get("orderbook/:symbol")
  @ApiOperation({ summary: "Get order book for a symbol" })
  @ApiResponse({ status: 200, description: "Returns order book" })
  async getOrderBook(
    @Param("symbol") symbol: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getOrderBook(symbol, apiKey, apiSecret, exchange);
  }

  @Post("orders")
  @ApiOperation({ summary: "Create a new order" })
  @ApiResponse({ status: 201, description: "Order created successfully" })
  async createOrder(
    @Body()
    order: {
      symbol: string;
      type: "limit" | "market";
      side: "buy" | "sell";
      amount: number;
      price?: number;
      apiKey: string;
      apiSecret: string;
      exchange?: string;
    },
  ) {
    const { apiKey, apiSecret, exchange = "kraken", ...orderParams } = order;
    return this.exchangesService.createOrder(orderParams, apiKey, apiSecret, exchange);
  }

  @Delete("orders/:orderId")
  @ApiOperation({ summary: "Cancel an order" })
  @ApiResponse({ status: 200, description: "Order canceled successfully" })
  async cancelOrder(
    @Param("orderId") orderId: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.cancelOrder(orderId, apiKey, apiSecret, exchange);
  }

  @Get("orders/:orderId")
  @ApiOperation({ summary: "Get order details" })
  @ApiResponse({ status: 200, description: "Returns order details" })
  async getOrder(
    @Param("orderId") orderId: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getOrder(orderId, apiKey, apiSecret, exchange);
  }

  @Get("orders")
  @ApiOperation({ summary: "Get open orders" })
  @ApiResponse({ status: 200, description: "Returns list of open orders" })
  async getOpenOrders(
    @Query("symbol") symbol: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getOpenOrders(apiKey, apiSecret, exchange, symbol);
  }

  @Get("orders/history")
  @ApiOperation({ summary: "Get order history" })
  @ApiResponse({ status: 200, description: "Returns order history" })
  async getOrderHistory(
    @Query("symbol") symbol: string,
    @Query("startTime") startTime: string,
    @Query("endTime") endTime: string,
    @Query("limit") limit: number,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getOrderHistory(
      {
        symbol,
        startTime: startTime ? new Date(startTime) : undefined,
        endTime: endTime ? new Date(endTime) : undefined,
        limit,
      },
      apiKey,
      apiSecret,
      exchange,
    );
  }

  @Get("balance")
  @ApiOperation({ summary: "Get account balance" })
  @ApiResponse({ status: 200, description: "Returns account balance" })
  async getBalance(
    @Query("asset") asset: string,
    @Query("apiKey") apiKey: string,
    @Query("apiSecret") apiSecret: string,
    @Query("exchange") exchange: string = "kraken",
  ) {
    return this.exchangesService.getBalance(apiKey, apiSecret, exchange, asset);
  }
}
