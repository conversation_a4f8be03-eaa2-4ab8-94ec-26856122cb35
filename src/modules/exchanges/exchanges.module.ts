import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ProductsModule } from "../products/products.module";
import { StepsModule } from "../steps/steps.module";
import { SubscriptionsModule } from "../subscriptions/subscriptions.module";
import { ExchangesService } from "./exchanges.service";
import { OrderCheckerService } from "./services/order-checker.service";
import { OrderStateService } from "./services/order-state.service";
import { PriceFeedService } from "./services/price-feed.service";

@Module({
  imports: [ConfigModule, forwardRef(() => SubscriptionsModule), ProductsModule, StepsModule],
  // controllers: [ExchangesController],
  providers: [
    {
      provide: "EXCHANGE",
      useFactory: () => {
        // We'll create a new exchange instance with the API credentials from the subscription
        return null; // This will be replaced with actual exchange instance when needed
      },
    },
    ExchangesService,
    PriceFeedService,
    OrderStateService,
    OrderCheckerService,
  ],
  exports: ["EXCHANGE", ExchangesService, PriceFeedService],
})
export class ExchangesModule {}
