import { Inject, Injectable, OnModule<PERSON><PERSON>roy, OnModuleInit } from "@nestjs/common";
import { Observable } from "rxjs";
import { KrakenExchange } from "./implementations/kraken.exchange";
import {
  Balance,
  ExchangeInterface,
  Order,
  OrderBook,
  Ticker,
} from "./interfaces/exchange.interface";

@Injectable()
export class ExchangesService implements OnModuleInit, OnModuleDestroy {
  private currentExchange: ExchangeInterface | null = null;

  constructor(
    @Inject("EXCHANGE")
    private readonly exchange: ExchangeInterface | null,
  ) {}

  async onModuleInit() {
    // No need to connect here as we'll connect when needed
  }

  async onModuleDestroy() {
    if (this.currentExchange) {
      await this.currentExchange.disconnect();
    }
  }

  private async ensureExchange(
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<ExchangeInterface> {
    if (!this.currentExchange || this.currentExchange.isConnected()) {
      if (this.currentExchange) {
        await this.currentExchange.disconnect();
      }

      switch (exchange.toLowerCase()) {
        case "kraken":
          this.currentExchange = new KrakenExchange(apiKey, apiSecret);
          break;
        default:
          throw new Error(`Unsupported exchange: ${exchange}`);
      }

      await this.currentExchange.connect();
    }

    return this.currentExchange;
  }

  // Market Data
  async getTicker(
    symbol: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<Ticker> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getTicker(symbol);
  }

  async getOrderBook(
    symbol: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<OrderBook> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getOrderBook(symbol);
  }

  subscribeToTicker(
    symbol: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Observable<Ticker> {
    return new Observable(subscriber => {
      this.ensureExchange(apiKey, apiSecret, exchange)
        .then(exchangeInstance => exchangeInstance.subscribeToTicker(symbol))
        .then(observable => observable.subscribe(subscriber))
        .catch(error => subscriber.error(error));
    });
  }

  subscribeToOrderBook(
    symbol: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Observable<OrderBook> {
    return new Observable(subscriber => {
      this.ensureExchange(apiKey, apiSecret, exchange)
        .then(exchangeInstance => exchangeInstance.subscribeToOrderBook(symbol))
        .then(observable => observable.subscribe(subscriber))
        .catch(error => subscriber.error(error));
    });
  }

  // Trading
  async createOrder(
    params: {
      symbol: string;
      type: "limit" | "market";
      side: "buy" | "sell";
      amount: number;
      price?: number;
      takeProfitPrice?: number;
    },
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<Order> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.createOrder(params);
  }

  async cancelOrder(
    orderId: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<boolean> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.cancelOrder(orderId);
  }

  async getOrder(
    orderId: string,
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<Order> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getOrder(orderId);
  }

  async getOpenOrders(
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
    symbol?: string,
  ): Promise<Order[]> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getOpenOrders(symbol);
  }

  async getOrderHistory(
    params: {
      symbol?: string;
      startTime?: Date;
      endTime?: Date;
      limit?: number;
    },
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
  ): Promise<Order[]> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getOrderHistory(params);
  }

  // Account
  async getBalance(
    apiKey: string,
    apiSecret: string,
    exchange: string = "kraken",
    asset?: string,
  ): Promise<Balance[]> {
    const exchangeInstance = await this.ensureExchange(apiKey, apiSecret, exchange);
    return exchangeInstance.getBalance(asset);
  }
}
