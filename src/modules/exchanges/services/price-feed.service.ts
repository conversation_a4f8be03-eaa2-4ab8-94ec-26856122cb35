import { Injectable, Lo<PERSON>, On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>, OnModuleInit } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Subscription as RxSubscription, Subject } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { ProductsService } from "../../products/products.service";
import { ExchangesService } from "../exchanges.service";
import { Ticker } from "../interfaces/exchange.interface";

interface ProductSubscription {
  symbol: string;
  subscription: RxSubscription;
}

/**
 * PriceFeedService is responsible solely for keeping the latest price of every
 * active product in memory and broadcasting an event each time a new price is
 * received. No API credentials are required because public endpoints are used.
 */
@Injectable()
export class PriceFeedService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PriceFeedService.name);
  private readonly latestPrices = new Map<string, number>();
  private readonly priceStream$ = new Subject<{ symbol: string; price: number }>();
  private readonly productSubscriptions: Map<string, ProductSubscription> = new Map();
  private refreshProductsInterval?: NodeJS.Timeout;

  constructor(
    private readonly exchangesService: ExchangesService,
    private readonly productsService: ProductsService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit(): Promise<void> {
    // Debounce raw stream to avoid flooding downstream consumers.
    this.priceStream$.pipe(debounceTime(500)).subscribe(({ symbol, price }) => {
      const prev = this.latestPrices.get(symbol);
      // Emit event only if price actually changed.
      if (prev !== price) {
        this.latestPrices.set(symbol, price);
        this.eventEmitter.emit("price.updated", { symbol, price });
      }

      // this.eventEmitter.emit("price.updated", { symbol, price });
    });

    await this.subscribeToActiveProducts();
    // Re-scan every minute to catch newly activated products.
    this.refreshProductsInterval = setInterval(
      () => this.subscribeToActiveProducts().catch(err => this.logger.error(err)),
      60_000,
    );
  }

  onModuleDestroy(): void {
    this.productSubscriptions.forEach(s => s.subscription.unsubscribe());
    clearInterval(this.refreshProductsInterval);
  }

  /**
   * Exposes the current snapshot of prices as a read-only map.
   */
  getCurrentPrices(): ReadonlyMap<string, number> {
    return this.latestPrices;
  }

  private async subscribeToActiveProducts(): Promise<void> {
    try {
      const products = await this.productsService.findAll();
      const active = products.filter(p => p.isActive);
      for (const { symbol } of active) {
        if (this.productSubscriptions.has(symbol)) continue;
        this.logger.debug(`Subscribing to ticker for ${symbol}`);
        const subscription = this.exchangesService
          .subscribeToTicker(symbol, "", "", "kraken") // public feed – no keys
          .subscribe({
            next: (ticker: Ticker) => {
              return this.priceStream$.next({ symbol, price: ticker.lastPrice });
            },
            error: err => {
              this.logger.warn(`Ticker error for ${symbol}: ${err}. Re-subscribing in 5 s.`);
              setTimeout(() => this.resubscribe(symbol), 5_000);
            },
          });
        this.productSubscriptions.set(symbol, { symbol, subscription });
      }
    } catch (error) {
      this.logger.error("Failed to subscribe to active products", error);
    }
  }

  private resubscribe(symbol: string): void {
    const existing = this.productSubscriptions.get(symbol);
    if (existing) {
      existing.subscription.unsubscribe();
      this.productSubscriptions.delete(symbol);
    }
    // Fire and forget – subscribe again.
    this.subscribeToActiveProducts().catch(err => this.logger.error(err));
  }
}
