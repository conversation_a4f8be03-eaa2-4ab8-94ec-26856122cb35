import { Injectable, <PERSON><PERSON>, OnM<PERSON>uleD<PERSON><PERSON>, OnModuleInit } from "@nestjs/common";
import { Subscription } from "../../subscriptions/entities/subscription.entity";
import { SubscriptionsService } from "../../subscriptions/subscriptions.service";
import { ExchangesService } from "../exchanges.service";
import { Order } from "../interfaces/exchange.interface";

interface UserOrderCache {
  lastFetch: number;
  orders: Order[];
}

/**
 * OrderStateService keeps an in-memory snapshot of each user's open orders and
 * refreshes the cache on a fixed schedule or on demand. This prevents hitting
 * the exchange API for every price tick.
 */
@Injectable()
export class OrderStateService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(OrderStateService.name);
  private readonly cache = new Map<number, UserOrderCache>();
  private readonly refreshing = new Set<number>();
  private timer?: NodeJS.Timeout;

  constructor(
    private readonly exchangesService: ExchangesService,
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  onModuleInit(): void {
    // Kick off periodic refresh.
    this.timer = setInterval(
      () => this.scheduledRefresh().catch(err => this.logger.error(err)),
      30000,
    );
  }

  onModuleDestroy(): void {
    clearInterval(this.timer);
  }

  /**
   * Returns cached orders for a user or an empty array.
   */
  getOrders(userId: number): Order[] {
    return this.cache.get(userId)?.orders ?? [];
  }

  /**
   * Force-refresh a single user's orders (e.g. after a trade was placed).
   */
  async refresh(subscription: Subscription): Promise<void> {
    const { userId } = subscription;
    if (this.refreshing.has(userId)) return;
    this.refreshing.add(userId);
    try {
      const orders = await this.exchangesService.getOpenOrders(
        subscription.apiKey.apiKey,
        subscription.apiKey.apiSecret,
        subscription.apiKey.exchange,
      );
      this.cache.set(userId, { lastFetch: Date.now(), orders });
    } catch (error) {
      this.logger.error(`Failed to refresh orders for user ${userId}`, error);
    } finally {
      this.refreshing.delete(userId);
    }
  }

  /**
   * Periodically refresh open orders for every active subscriber.
   */
  private async scheduledRefresh(): Promise<void> {
    try {
      this.logger.debug("Refreshing order cache");
      const subs = await this.subscriptionsService.findAllActive();
      const byUser = new Map<number, Subscription>();
      for (const s of subs) {
        if (!byUser.has(s.userId)) byUser.set(s.userId, s); // one sub per user is enough to get credentials
      }
      for (const sub of byUser.values()) {
        await this.refresh(sub);
      }
    } catch (error) {
      this.logger.error("Order cache scheduled refresh failed", error);
    }
  }
}
