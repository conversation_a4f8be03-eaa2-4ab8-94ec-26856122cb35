import { Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { Step } from "../../steps/entities/step.entity";
import { StepsService } from "../../steps/steps.service";
import { Subscription } from "../../subscriptions/entities/subscription.entity";
import { SubscriptionsService } from "../../subscriptions/subscriptions.service";
import { ExchangesService } from "../exchanges.service";
import { Order } from "../interfaces/exchange.interface";
import { OrderStateService } from "./order-state.service";
import { PriceFeedService } from "./price-feed.service";

@Injectable()
export class OrderCheckerService {
  private readonly logger = new Logger(OrderCheckerService.name);
  private readonly userLocks = new Set<number>();

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly stepsService: StepsService,
    private readonly orderState: OrderStateService,
    private readonly exchangesService: ExchangesService,
    private readonly priceFeed: PriceFeedService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private interval?: NodeJS.Timeout;

  onModuleInit(): void {
    // Fallback: every 10 seconds evaluate all latest prices regardless of change.
    this.interval = setInterval(() => this.fullScan().catch(err => this.logger.error(err)), 10_000);
  }

  onModuleDestroy(): void {
    clearInterval(this.interval);
  }

  /**
   * Reacts to every price update emitted by PriceFeedService.
   */
  @OnEvent("price.updated")
  async handlePriceUpdate({ symbol, price }: { symbol: string; price: number }): Promise<void> {
    this.logger.debug(`Price update for ${symbol}: ${price}`);
    try {
      const subscriptions = await this.subscriptionsService.findActiveBySymbol(symbol);
      const grouped = this.groupByUser(subscriptions);

      for (const [userId, userSubs] of grouped) {
        // Prevent concurrent checks per user
        if (this.userLocks.has(userId)) continue;
        this.userLocks.add(userId);
        try {
          await this.evaluateUser(userSubs, price);
        } finally {
          this.userLocks.delete(userId);
        }
      }
    } catch (error) {
      this.logger.error(`Price check failed for ${symbol}`, error);
    }
  }

  private async evaluateUser(subs: Subscription[], price: number): Promise<void> {
    const userId = subs[0].userId;
    const openOrders = this.orderState.getOrders(userId);

    for (const sub of subs) {
      const symbol = sub.strategy.product.symbol;
      const step = await this.stepsService.getClosestStep(price, symbol);
      if (!step) continue;

      const hasOpenForStep = this.hasOrderForStep(openOrders, symbol, step);
      this.logger.debug(`${symbol} has open for step ${JSON.stringify(step)}: ${hasOpenForStep}`);
      if (hasOpenForStep) continue;

      await this.placeOrder(sub, step);
    }
  }

  private hasOrderForStep(orders: Order[], symbol: string, step: Step): boolean {
    const formattedSymbol = symbol.replace("/", "");
    return orders.some(o => {
      if (o.symbol !== formattedSymbol) return false;
      if (!o.price) return false;
      const buyMatch =
        o.side === "buy" && o.price <= step.buyPrice * 1.01 && o.price >= step.buyPrice * 0.99;
      const sellMatch =
        o.side === "sell" && o.price <= step.sellPrice * 1.01 && o.price >= step.sellPrice * 0.99;
      return buyMatch || sellMatch;
    });
  }

  private async placeOrder(sub: Subscription, step: Step): Promise<void> {
    try {
      const symbol = sub.strategy.product.symbol;
      const roundedBuyPrice = symbol.includes("BTC")
        ? Math.round(step.buyPrice * 10) / 10
        : step.buyPrice;
      const roundedSellPrice = symbol.includes("BTC")
        ? Math.round(step.sellPrice * 10) / 10
        : step.sellPrice;

      const amountUsd = Number(sub.amount) * (Number(step.amount) / 100);
      const volume = amountUsd / roundedBuyPrice;

      await this.exchangesService.createOrder(
        {
          symbol: symbol.replace("/", ""),
          type: "limit",
          side: "buy",
          amount: volume,
          price: roundedBuyPrice,
          takeProfitPrice: roundedSellPrice,
        },
        sub.apiKey.apiKey,
        sub.apiKey.apiSecret,
        sub.apiKey.exchange,
      );

      this.eventEmitter.emit("order.created", {
        symbol,
        amount: volume,
        buyPrice: roundedBuyPrice,
        sellPrice: roundedSellPrice,
        userId: sub.userId,
        strategyId: sub.strategyId,
        createdAt: new Date(),
      });
      // Refresh cache so next evaluation has fresh data
      await this.orderState.refresh(sub);
    } catch (error) {
      this.logger.error(`Failed to place order for subscription ${sub.id}`, error);
    }
  }

  private groupByUser(subs: Subscription[]): Map<number, Subscription[]> {
    const map = new Map<number, Subscription[]>();
    for (const s of subs) {
      const list = map.get(s.userId) ?? [];
      list.push(s);
      map.set(s.userId, list);
    }
    return map;
  }

  /**
   * Runs through the current price snapshot and performs the same evaluation
   * logic to honour the requirement of checking every 10 seconds.
   */
  private async fullScan(): Promise<void> {
    const prices = this.priceFeed.getCurrentPrices();
    for (const [symbol, price] of prices) {
      await this.handlePriceUpdate({ symbol, price });
    }
  }
}
