import { Observable } from "rxjs";

export interface OrderBook {
  bids: [number, number][]; // [price, volume]
  asks: [number, number][]; // [price, volume]
}

export interface Order {
  id: string;
  symbol: string;
  type: "limit" | "market";
  side: "buy" | "sell";
  price?: number;
  amount: number;
  status: "open" | "closed" | "canceled";
  filledAmount: number;
  remainingAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Balance {
  asset: string;
  free: number;
  locked: number;
}

export interface Ticker {
  symbol: string;
  lastPrice: number;
  bid: number;
  ask: number;
  volume24h: number;
  priceChange24h: number;
  priceChangePercent24h: number;
}

export interface ExchangeInterface {
  // Market Data
  getTicker(symbol: string): Promise<Ticker>;
  getOrderBook(symbol: string): Promise<OrderBook>;
  subscribeToTicker(symbol: string): Observable<Ticker>;
  subscribeToOrderBook(symbol: string): Observable<OrderBook>;

  // Trading
  createOrder(params: {
    symbol: string;
    type: "limit" | "market";
    side: "buy" | "sell";
    amount: number;
    price?: number;
    takeProfitPrice?: number;
  }): Promise<Order>;

  cancelOrder(orderId: string): Promise<boolean>;
  getOrder(orderId: string): Promise<Order>;
  getOpenOrders(symbol?: string): Promise<Order[]>;
  getOrderHistory(params: {
    symbol?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
  }): Promise<Order[]>;

  // Account
  getBalance(asset?: string): Promise<Balance[]>;

  // Connection Management
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
}
