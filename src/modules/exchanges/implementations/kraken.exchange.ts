import { Injectable, Logger } from "@nestjs/common";
import axios from "axios";
import * as crypto from "crypto";
import { Observable } from "rxjs";
import {
  Balance,
  ExchangeInterface,
  Order,
  OrderBook,
  Ticker,
} from "../interfaces/exchange.interface";

interface KrakenTickerResponse {
  result: Record<
    string,
    {
      c: string[];
      b: string[];
      a: string[];
      v: string[];
      p: string[];
    }
  >;
  error: string[];
}

interface KrakenOrderBookResponse {
  result: Record<
    string,
    {
      bids: [string, string, number][];
      asks: [string, string, number][];
    }
  >;
  error: string[];
}

interface KrakenOrderQuery {
  pair: string;
  ordertype: string;
  type: string;
  price: string;
  vol: string;
  vol_exec: string;
  status: string;
  opentm: number;
  closetm: number;
}

interface KrakenBalanceResponse {
  result: Record<string, string>;
  error: string[];
}

@Injectable()
export class KrakenExchange implements ExchangeInterface {
  private readonly logger = new Logger(KrakenExchange.name);
  private readonly baseUrl = "https://api.kraken.com";
  private readonly version = "/0";
  /**
   * The last used nonce value to ensure strictly increasing nonces for Kraken API requests.
   */
  private lastNonce = 0;

  constructor(
    private readonly apiKey: string,
    private readonly apiSecret: string,
  ) {}

  /**
   * Generates a strictly increasing nonce for Kraken API requests.
   * Ensures that the nonce is always greater than the previous one, even for rapid or parallel requests.
   * @returns {number} The next nonce value.
   */
  private generateNonce(): number {
    const now = Date.now() * 1000;
    if (now <= this.lastNonce) {
      this.lastNonce += 1;
    } else {
      this.lastNonce = now;
    }
    return this.lastNonce;
  }

  private generateSignature(path: string, nonce: number, data: Record<string, any>): string {
    const postData = new URLSearchParams({ ...data, nonce: nonce.toString() }).toString();
    const message = Buffer.concat([
      Buffer.from(path),
      crypto
        .createHash("sha256")
        .update(nonce + postData)
        .digest(),
    ]);
    const secret = Buffer.from(this.apiSecret, "base64");
    return crypto.createHmac("sha512", secret).update(message).digest("base64");
  }

  private async privateRequest(endpoint: string, data: Record<string, any> = {}) {
    const nonce = this.generateNonce();
    const path = `${this.version}${endpoint}`;
    const signature = this.generateSignature(path, nonce, data);

    const headers = {
      "API-Key": this.apiKey,
      "API-Sign": signature,
    };

    try {
      const response = await axios.post(
        `${this.baseUrl}${path}`,
        new URLSearchParams({ ...data, nonce: nonce.toString() }),
        { headers },
      );

      this.logger.warn("Kraken response");
      this.logger.log(`URL: ${`${this.baseUrl}${path}`}`);
      this.logger.log(`Data: ${JSON.stringify(data)}`);
      this.logger.warn("Kraken response end");

      if (response.data.error?.length)
        throw new Error(response.data.error.map((e: any) => e.msg).join(", "));

      return response.data;
    } catch (e) {
      this.logger.error(`Private request failed at ${endpoint}:`, e);
      throw e;
    }
  }

  private async publicRequest(endpoint: string, params: Record<string, any> = {}) {
    try {
      const response = await axios.get(`${this.baseUrl}${this.version}${endpoint}`, { params });
      if (response.data.error?.length) throw new Error(response.data.error.join(", "));
      return response.data;
    } catch (e) {
      this.logger.error(`Public request failed at ${endpoint}:`, e);
      throw e;
    }
  }

  async connect(): Promise<void> {
    this.logger.log("Kraken REST connected");
  }

  async disconnect(): Promise<void> {
    this.logger.log("Kraken REST disconnected");
  }

  isConnected(): boolean {
    return true;
  }

  async getTicker(symbol: string): Promise<Ticker> {
    const { result } = (await this.publicRequest("/public/Ticker", {
      pair: symbol,
    })) as KrakenTickerResponse;
    const ticker = result[symbol];
    return {
      symbol,
      lastPrice: parseFloat(ticker.c[0]),
      bid: parseFloat(ticker.b[0]),
      ask: parseFloat(ticker.a[0]),
      volume24h: parseFloat(ticker.v[1]),
      priceChange24h: parseFloat(ticker.p[1]) - parseFloat(ticker.p[0]),
      priceChangePercent24h:
        ((parseFloat(ticker.p[1]) - parseFloat(ticker.p[0])) / parseFloat(ticker.p[0])) * 100,
    };
  }

  async getOrderBook(symbol: string): Promise<OrderBook> {
    const { result } = (await this.publicRequest("/public/Depth", {
      pair: symbol,
      count: 100,
    })) as KrakenOrderBookResponse;
    const data = result[symbol];
    return {
      bids: data.bids.map(([price, volume]) => [parseFloat(price), parseFloat(volume)]),
      asks: data.asks.map(([price, volume]) => [parseFloat(price), parseFloat(volume)]),
    };
  }

  subscribeToTicker(symbol: string): Observable<Ticker> {
    return new Observable<Ticker>(subscriber => {
      // Poll the ticker endpoint every 5 seconds
      const interval = setInterval(async () => {
        try {
          const ticker = await this.getTicker(symbol);
          subscriber.next(ticker);
        } catch (error) {
          this.logger.error(`Error fetching ticker for ${symbol}:`, error);
          subscriber.error(error);
        }
      }, 10000);

      // Initial ticker fetch
      this.getTicker(symbol)
        .then(ticker => subscriber.next(ticker))
        .catch(error => {
          this.logger.error(`Error fetching initial ticker for ${symbol}:`, error);
          subscriber.error(error);
        });

      // Cleanup function
      return () => {
        clearInterval(interval);
      };
    });
  }

  subscribeToOrderBook(symbol: string): Observable<OrderBook> {
    return new Observable<OrderBook>(subscriber => {
      // Poll the order book endpoint every 5 seconds
      const interval = setInterval(async () => {
        try {
          const orderBook = await this.getOrderBook(symbol);
          subscriber.next(orderBook);
        } catch (error) {
          this.logger.error(`Error fetching order book for ${symbol}:`, error);
          subscriber.error(error);
        }
      }, 5000);

      // Initial order book fetch
      this.getOrderBook(symbol)
        .then(orderBook => subscriber.next(orderBook))
        .catch(error => {
          this.logger.error(`Error fetching initial order book for ${symbol}:`, error);
          subscriber.error(error);
        });

      // Cleanup function
      return () => {
        clearInterval(interval);
      };
    });
  }

  async createOrder({
    symbol,
    type,
    side,
    amount,
    price,
    takeProfitPrice,
  }: {
    symbol: string;
    type: "limit" | "market";
    side: "buy" | "sell";
    amount: number;
    price?: number;
    takeProfitPrice?: number;
  }): Promise<Order> {
    const payload: Record<string, string> = {
      pair: symbol,
      type: side,
      ordertype: type,
      volume: amount.toString(),
      price: price?.toString() || "",
      "close[ordertype]": "take-profit",
      "close[price]": takeProfitPrice?.toString() || "",
    };
    if (type === "limit" && price) payload.price = price.toString();

    const { result } = await this.privateRequest("/private/AddOrder", payload);
    const orderId = result.txid[0];
    return this.getOrder(orderId);
  }

  async cancelOrder(orderId: string): Promise<boolean> {
    await this.privateRequest("/private/CancelOrder", { txid: orderId });
    return true;
  }

  async getOrder(orderId: string): Promise<Order> {
    const { result } = await this.privateRequest("/private/QueryOrders", { txid: orderId });
    const order = result[orderId];
    return this.mapToOrder(orderId, order);
  }

  async getOpenOrders(): Promise<Order[]> {
    const { result } = await this.privateRequest("/private/OpenOrders");
    return Object.entries(result.open || {}).map(([id, o]) =>
      this.mapToOrder(id, o as KrakenOrderQuery),
    );
  }

  async getOrderHistory({
    symbol,
    startTime,
    endTime,
    limit,
  }: {
    symbol?: string;
    startTime?: Date;
    endTime?: Date;
    limit?: number;
  }): Promise<Order[]> {
    const params: Record<string, any> = {};
    if (symbol) params.pair = symbol;
    if (startTime) params.start = Math.floor(startTime.getTime() / 1000);
    if (endTime) params.end = Math.floor(endTime.getTime() / 1000);
    if (limit) params.limit = limit;

    const { result } = await this.privateRequest("/private/ClosedOrders", params);
    return Object.entries(result.closed || {}).map(([id, o]) =>
      this.mapToOrder(id, o as KrakenOrderQuery),
    );
  }

  async getBalance(asset?: string): Promise<Balance[]> {
    const { result } = (await this.privateRequest("/private/Balance")) as KrakenBalanceResponse;
    return Object.entries(result)
      .map(([a, amt]) => ({
        asset: a,
        free: parseFloat(amt),
        locked: 0,
      }))
      .filter(b => !asset || b.asset === asset);
  }

  private mapToOrder(id: string, o: KrakenOrderQuery): Order {
    // Handle both order formats - the standard KrakenOrderQuery and the expanded format from OpenOrders
    const orderDescr = (o as any).descr;

    const pair = orderDescr?.pair || o.pair;

    return {
      id,
      symbol: pair === "XBTUSD" ? "BTCUSD" : pair,
      type: orderDescr?.ordertype || (o.ordertype as "limit" | "market"),
      side: orderDescr?.type || (o.type as "buy" | "sell"),
      price: parseFloat(orderDescr?.price || o.price || "0"),
      amount: parseFloat(o.vol),
      filledAmount: parseFloat(o.vol_exec),
      remainingAmount: parseFloat(o.vol) - parseFloat(o.vol_exec),
      status: this.mapStatus(o.status),
      createdAt: new Date(o.opentm * 1000),
      updatedAt: new Date((o.closetm || o.opentm) * 1000),
    };
  }

  private mapStatus(status: string): "open" | "closed" | "canceled" {
    if (status === "closed") return "closed";
    if (status === "canceled") return "canceled";
    return "open";
  }
}
