import { Injectable, NotFoundException, UnauthorizedException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { User } from "../users/entities/user.entity";
import { CreatePaymentDto } from "./dto/create-payment.dto";
import { UpdatePaymentDto } from "./dto/update-payment.dto";
import { Payment } from "./entities/payment.entity";

@Injectable()
export class PaymentsService {
  constructor(
    @InjectRepository(Payment)
    private paymentsRepository: Repository<Payment>,
  ) {}

  create(createPaymentDto: CreatePaymentDto, user: Partial<User>) {
    const payment = this.paymentsRepository.create({
      ...createPaymentDto,
      user,
    });
    return this.paymentsRepository.save(payment);
  }

  findAll(user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.paymentsRepository.find({
      where: { user: { id: user.id } },
      order: { createdAt: "DESC" },
    });
  }

  async findOne(id: number, user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    const payment = await this.paymentsRepository.findOne({
      where: { id, user: { id: user.id } },
    });

    if (!payment) {
      throw new NotFoundException(`Payment with ID ${id} not found`);
    }

    return payment;
  }

  async update(id: number, updatePaymentDto: UpdatePaymentDto, user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    const payment = await this.findOne(id, user);
    Object.assign(payment, updatePaymentDto);
    return this.paymentsRepository.save(payment);
  }

  async remove(id: number, user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    const payment = await this.findOne(id, user);
    return this.paymentsRepository.remove(payment);
  }
}
