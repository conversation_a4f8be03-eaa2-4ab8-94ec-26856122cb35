import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UnauthorizedException,
  UseGuards,
} from "@nestjs/common";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { Roles } from "src/modules/auth/decorators/roles.decorator";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/modules/auth/guards/roles.guard";
import { User, UserRole } from "src/modules/users/entities/user.entity";
import { CreatePaymentDto } from "./dto/create-payment.dto";
import { UpdatePaymentDto } from "./dto/update-payment.dto";
import { PaymentsService } from "./payments.service";

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller("payments")
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @Roles(UserRole.USER, UserRole.ADMIN)
  create(@Body() createPaymentDto: CreatePaymentDto, @CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.paymentsService.create(createPaymentDto, user);
  }

  @Get()
  @Roles(UserRole.USER, UserRole.ADMIN)
  findAll(@CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.paymentsService.findAll(user);
  }

  @Get(":id")
  @Roles(UserRole.USER)
  findOne(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    return this.paymentsService.findOne(+id, user);
  }

  @Put(":id")
  @Roles(UserRole.USER)
  update(
    @Param("id") id: string,
    @Body() updatePaymentDto: UpdatePaymentDto,
    @CurrentUser() user: Partial<User>,
  ) {
    return this.paymentsService.update(+id, updatePaymentDto, user);
  }

  @Delete(":id")
  @Roles(UserRole.USER)
  remove(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    return this.paymentsService.remove(+id, user);
  }
}
