import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Product } from "../products/entities/product.entity";
import { Strategy } from "../strategies/entities/strategy.entity";
import { User, UserRole } from "../users/entities/user.entity";

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(Strategy)
    private readonly strategyRepository: Repository<Strategy>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  // Product CRUD operations
  async createProduct(data: Partial<Product>): Promise<Product> {
    const product = this.productRepository.create(data);
    return this.productRepository.save(product);
  }

  async updateProduct(id: number, data: Partial<Product>): Promise<Product> {
    const product = await this.productRepository.findOne({ where: { id } });
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    Object.assign(product, data);
    return this.productRepository.save(product);
  }

  async deleteProduct(id: number): Promise<void> {
    const result = await this.productRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
  }

  async getProduct(id: number): Promise<Product> {
    const product = await this.productRepository.findOne({ where: { id } });
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    return product;
  }

  async getAllProducts(): Promise<Product[]> {
    return this.productRepository.find();
  }

  // Strategy CRUD operations
  async createStrategy(data: Partial<Strategy>): Promise<Strategy> {
    const strategy = this.strategyRepository.create(data);
    return this.strategyRepository.save(strategy);
  }

  async updateStrategy(id: number, data: Partial<Strategy>): Promise<Strategy> {
    const strategy = await this.strategyRepository.findOne({ where: { id } });
    if (!strategy) {
      throw new NotFoundException(`Strategy with ID ${id} not found`);
    }
    Object.assign(strategy, data);
    return this.strategyRepository.save(strategy);
  }

  async deleteStrategy(id: number): Promise<void> {
    const result = await this.strategyRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Strategy with ID ${id} not found`);
    }
  }

  async getStrategy(id: number): Promise<Strategy> {
    const strategy = await this.strategyRepository.findOne({ where: { id } });
    if (!strategy) {
      throw new NotFoundException(`Strategy with ID ${id} not found`);
    }
    return strategy;
  }

  async getAllStrategies(): Promise<Strategy[]> {
    return this.strategyRepository.find({ relations: ["product"] });
  }

  // User management
  async updateUserRole(userId: number, role: UserRole): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
    user.role = role;
    return this.userRepository.save(user);
  }

  async getUsers(): Promise<User[]> {
    return this.userRepository.find();
  }
}
