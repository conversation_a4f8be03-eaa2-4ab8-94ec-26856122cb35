import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AdminController } from "src/modules/admin/admin.controller";
import { AdminService } from "src/modules/admin/admin.service";
import { Product } from "../products/entities/product.entity";
import { Strategy } from "../strategies/entities/strategy.entity";
import { User } from "../users/entities/user.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Product, Strategy, User])],
  controllers: [AdminController],
  providers: [AdminService],
  exports: [AdminService],
})
export class AdminModule {}
