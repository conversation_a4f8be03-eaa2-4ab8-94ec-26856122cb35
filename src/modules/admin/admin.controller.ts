import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Throttle } from "@nestjs/throttler";
import { Roles } from "../auth/decorators/roles.decorator";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Product } from "../products/entities/product.entity";
import { Strategy } from "../strategies/entities/strategy.entity";
import { User, UserRole } from "../users/entities/user.entity";
import { AdminService } from "./admin.service";

@ApiTags("admin")
@Controller("admin")
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@Throttle({ default: { limit: 50, ttl: 60 } })
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  // Product endpoints
  @Post("products")
  async createProduct(@Body() data: Partial<Product>): Promise<Product> {
    return this.adminService.createProduct(data);
  }

  @Put("products/:id")
  async updateProduct(@Param("id") id: number, @Body() data: Partial<Product>): Promise<Product> {
    return this.adminService.updateProduct(id, data);
  }

  @Delete("products/:id")
  async deleteProduct(@Param("id") id: number): Promise<void> {
    return this.adminService.deleteProduct(id);
  }

  @Get("products/:id")
  async getProduct(@Param("id") id: number): Promise<Product> {
    return this.adminService.getProduct(id);
  }

  @Get("products")
  async getAllProducts(): Promise<Product[]> {
    return this.adminService.getAllProducts();
  }

  // Strategy endpoints
  @Post("strategies")
  @ApiOperation({ summary: "Create a new strategy" })
  @ApiResponse({ status: 201, description: "Strategy created successfully" })
  async createStrategy(@Body() data: Partial<Strategy>): Promise<Strategy> {
    return this.adminService.createStrategy(data);
  }

  @Put("strategies/:id")
  async updateStrategy(
    @Param("id") id: number,
    @Body() data: Partial<Strategy>,
  ): Promise<Strategy> {
    return this.adminService.updateStrategy(id, data);
  }

  @Delete("strategies/:id")
  async deleteStrategy(@Param("id") id: number): Promise<void> {
    return this.adminService.deleteStrategy(id);
  }

  @Get("strategies/:id")
  async getStrategy(@Param("id") id: number): Promise<Strategy> {
    return this.adminService.getStrategy(id);
  }

  @Get("strategies")
  @ApiOperation({ summary: "Get all strategies" })
  @ApiResponse({ status: 200, description: "Returns all strategies" })
  async getAllStrategies() {
    return this.adminService.getAllStrategies();
  }

  // User management
  @Put("users/:id/role")
  async updateUserRole(@Param("id") userId: number, @Body("role") role: UserRole): Promise<User> {
    return this.adminService.updateUserRole(userId, role);
  }

  @Get("users")
  @ApiOperation({ summary: "Get all users" })
  @ApiResponse({ status: 200, description: "Returns all users" })
  async getAllUsers() {
    return this.adminService.getUsers();
  }
}
