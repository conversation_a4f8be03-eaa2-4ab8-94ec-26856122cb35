import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Order } from "./entities/order.entity";
import { OrdersListener } from "./orders.listener";
import { OrdersService } from "./orders.service";
@Module({
  imports: [TypeOrmModule.forFeature([Order])],
  providers: [OrdersService, OrdersListener],
  exports: [OrdersService],
})
export class OrdersModule {}
