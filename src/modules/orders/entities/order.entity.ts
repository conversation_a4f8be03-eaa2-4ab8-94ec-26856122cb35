import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("orders")
export class Order {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  strategyId!: number;

  @Column()
  symbol!: string;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  amount!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  buyPrice!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  sellPrice!: number;

  @Column()
  userId!: number;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
