import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { CreateOrderDto } from "./dto/create-order.dto";
import { OrderCreatedEvent } from "./order.events";
import { OrdersService } from "./orders.service";

@Injectable()
export class OrdersListener {
  constructor(private readonly ordersService: OrdersService) {}

  @OnEvent("order.created")
  async handleOrderCreatedEvent(event: OrderCreatedEvent) {
    const dto: CreateOrderDto = {
      strategyId: event.strategyId,
      symbol: event.symbol,
      amount: event.amount,
      buyPrice: event.buyPrice,
      sellPrice: event.sellPrice,
      createdAt: event.createdAt,
      userId: event.userId,
    };
    await this.ordersService.createOrder(dto);
  }
}
