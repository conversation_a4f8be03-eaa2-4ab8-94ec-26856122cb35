import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersService } from "./users.service";
import { User } from "./entities/user.entity";
import { VerificationCode } from "./entities/verification-code.entity";
import { UserRepository } from "./repositories/user.repository";
import { VerificationModule } from "../verification/verification.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([User, VerificationCode]),
    VerificationModule,
  ],
  providers: [UsersService, UserRepository],
  exports: [UsersService],
})
export class UsersModule {}
