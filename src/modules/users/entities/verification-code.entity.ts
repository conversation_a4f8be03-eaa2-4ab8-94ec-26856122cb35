import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
} from "typeorm";
import { User } from "./user.entity";

@Entity()
export class VerificationCode {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  code!: string;

  @Column()
  userId!: number;

  @ManyToOne(() => User, (user) => user.verificationCodes)
  user!: User;

  @CreateDateColumn()
  createdAt!: Date;
}
