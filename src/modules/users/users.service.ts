import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AppLogger } from "../../common/logger/app.logger";
import { User } from "./entities/user.entity";
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,
    private readonly logger: AppLogger,
  ) {}

  async findByEmail(email: string): Promise<User | null> {
    try {
      return this.usersRepository.findOne({ where: { email } });
    } catch (error) {
      throw error;
    }
  }

  async create(userData: {
    email: string;
    password: string;
    verificationCode?: string;
    verificationExpires?: Date;
    isVerified?: boolean;
  }): Promise<User> {
    try {
      const user = this.usersRepository.create(userData);
      return this.usersRepository.save(user);
    } catch (error: any) {
      this.logger.error(`Failed to create user: ${error.message}`);
      throw error;
    }
  }

  async findById(id: number): Promise<User | null> {
    try {
      return this.usersRepository.findOne({ where: { id } });
    } catch (error: any) {
      this.logger.error(`Failed to find user by ID: ${error.message}`);
      throw error;
    }
  }

  async update(id: number, data: Partial<User>): Promise<User> {
    try {
      await this.usersRepository.update(id, data);
      const updatedUser = await this.usersRepository.findOne({ where: { id } });
      if (!updatedUser) {
        throw new Error(`User with ID ${id} not found after update`);
      }
      return updatedUser;
    } catch (error: any) {
      this.logger.error(`Failed to update user: ${error.message}`);
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await this.usersRepository.delete(id);
    } catch (error: any) {
      this.logger.error(`Failed to delete user: ${error.message}`);
      throw error;
    }
  }
}
