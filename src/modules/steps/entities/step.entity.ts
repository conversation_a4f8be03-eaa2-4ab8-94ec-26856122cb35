import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Product } from "../../products/entities/product.entity";

@Entity("steps")
export class Step {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => Product)
  product!: Product;

  @Column()
  productId!: number;

  @Column()
  stepNumber!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  buyPrice!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  sellPrice!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  amount!: number;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
