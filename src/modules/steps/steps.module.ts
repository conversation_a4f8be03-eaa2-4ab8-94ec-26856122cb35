import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Step } from "./entities/step.entity";
import { StepsController } from "./steps.controller";
import { StepsService } from "./steps.service";

@Module({
  imports: [TypeOrmModule.forFeature([Step])],
  controllers: [StepsController],
  providers: [StepsService],
  exports: [StepsService],
})
export class StepsModule {}
