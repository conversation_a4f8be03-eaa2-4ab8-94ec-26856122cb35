import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { LessThan, Repository } from "typeorm";
import { Step } from "./entities/step.entity";

@Injectable()
export class StepsService {
  constructor(
    @InjectRepository(Step)
    private readonly stepRepository: Repository<Step>,
  ) {}

  getClosestStep(price: number, symbol: string): Promise<Step | null> {
    return this.stepRepository.findOne({
      where: { buyPrice: LessThan(price), product: { symbol } },
      order: { buyPrice: "DESC" },
    });
  }

  async create(createStepDto: Partial<Step>): Promise<Step> {
    const step = this.stepRepository.create(createStepDto);
    return this.stepRepository.save(step);
  }

  async removeByProductId(productId: number): Promise<void> {
    await this.stepRepository.delete({ productId });
  }

  async findAll(): Promise<Step[]> {
    return this.stepRepository.find({
      relations: ["product"],
      order: {
        productId: "ASC",
        stepNumber: "ASC",
      },
    });
  }

  async findByProduct(productId: number): Promise<Step[]> {
    return this.stepRepository.find({
      where: { productId },
      relations: ["product"],
      order: { stepNumber: "ASC" },
    });
  }

  async findOne(id: number): Promise<Step> {
    const step = await this.stepRepository.findOne({
      where: { id },
      relations: ["product"],
    });

    if (!step) {
      throw new NotFoundException(`Step with ID ${id} not found`);
    }

    return step;
  }

  async update(id: number, updateStepDto: Partial<Step>): Promise<Step> {
    const step = await this.findOne(id);
    Object.assign(step, updateStepDto);
    return this.stepRepository.save(step);
  }

  async remove(id: number): Promise<void> {
    const step = await this.findOne(id);
    await this.stepRepository.remove(step);
  }
}
