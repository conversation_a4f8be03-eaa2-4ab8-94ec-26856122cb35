import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Step } from "./entities/step.entity";
import { StepsService } from "./steps.service";

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller("steps")
export class StepsController {
  constructor(private readonly stepsService: StepsService) {}

  @Post()
  create(@Body() createStepDto: Partial<Step>) {
    return this.stepsService.create(createStepDto);
  }

  @Get()
  findAll() {
    return this.stepsService.findAll();
  }

  @Get("product/:productId")
  findByProduct(@Param("productId") productId: string) {
    return this.stepsService.findByProduct(+productId);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.stepsService.findOne(+id);
  }

  @Put(":id")
  update(@Param("id") id: string, @Body() updateStepDto: Partial<Step>) {
    return this.stepsService.update(+id, updateStepDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.stepsService.remove(+id);
  }
}
