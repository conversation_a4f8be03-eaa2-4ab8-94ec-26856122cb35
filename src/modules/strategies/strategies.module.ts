import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { OrdersModule } from "../orders";
import { StepsModule } from "../steps/steps.module";
import { Strategy } from "./entities/strategy.entity";
import { StrategiesController } from "./strategies.controller";
import { StrategiesService } from "./strategies.service";

@Module({
  imports: [TypeOrmModule.forFeature([Strategy]), StepsModule, OrdersModule],
  controllers: [StrategiesController],
  providers: [StrategiesService],
  exports: [StrategiesService],
})
export class StrategiesModule {}
