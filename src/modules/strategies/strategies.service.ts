import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { btcData, suiData } from "src/modules/strategies/pair_data";
import { Repository } from "typeorm";
import { StepsService } from "../steps/steps.service";
import { CreateStrategyDto } from "./dto/create-strategy.dto";
import { UpdateStrategyDto } from "./dto/update-strategy.dto";
import { Strategy } from "./entities/strategy.entity";

@Injectable()
export class StrategiesService {
  private readonly logger = new Logger(StrategiesService.name);
  private readonly MAX_STEPS = 20; // Maximum number of steps to prevent memory issues

  constructor(
    @InjectRepository(Strategy)
    private strategiesRepository: Repository<Strategy>,
    private readonly stepsService: StepsService,
  ) {}

  /**
   * Generate steps for a strategy. If the product symbol is 'BTC', use btcData for steps; otherwise, generate steps dynamically.
   * @param strategy The strategy entity with product relation loaded
   */
  private async generateSteps(strategy: Strategy): Promise<void> {
    this.logger.log(`Removing steps for product ${strategy.productId}`);
    this.logger.log(`strategy ${JSON.stringify(strategy)}`);
    await this.stepsService.removeByProductId(strategy.productId);

    // Use btcData if the pair is BTC
    if (strategy.product.symbol === "BTC/USD") {
      this.logger.log(`Generating ${btcData.length} BTC steps from btcData`);
      for (let i = 0; i < btcData.length; i++) {
        const item = btcData[i];
        const stepNumber = i + 1;
        this.logger.log(
          `${stepNumber}: buyPrice ${item.buy} | sellPrice ${item.sell} | amount ${item.percentage}`,
        );
        await this.stepsService.create({
          productId: strategy.productId,
          stepNumber,
          buyPrice: item.buy,
          sellPrice: item.sell,
          amount: item.percentage,
        });
      }
      return;
    }
    // Use btcData if the pair is BTC
    if (strategy.product.symbol === "SUI/USD") {
      this.logger.log(`Generating ${suiData.length} SUI steps from suiData`);
      for (let i = 0; i < suiData.length; i++) {
        const item = suiData[i];
        const stepNumber = i + 1;
        this.logger.log(
          `${stepNumber}: buyPrice ${item.buy} | sellPrice ${item.sell} | amount ${item.percentage}`,
        );
        await this.stepsService.create({
          productId: strategy.productId,
          stepNumber,
          buyPrice: Number(item.buy.toFixed(2)),
          sellPrice: Number(item.sell.toFixed(2)),
          amount: item.percentage,
        });
      }
      return;
    }

    // Default: dynamic step generation
    const ATH = Number(strategy.product.priceUsd);
    const profitMultiplier = Number(strategy.profitPercentage);
    const decrementPercentage = profitMultiplier / 100;
    const stepPrices: number[] = [];
    let currentPrice = ATH;
    for (let i = 0; i < this.MAX_STEPS && currentPrice >= 1; i++) {
      stepPrices.push(currentPrice);
      currentPrice = currentPrice * (1 - decrementPercentage);
    }
    this.logger.log(`Generating ${stepPrices.length - 1} steps`);
    for (let i = 0; i < stepPrices.length - 1; i++) {
      const sellPrice = Number(stepPrices[i]).toFixed(2);
      const buyPrice = Number(stepPrices[i + 1]).toFixed(2);
      const amount = Number(strategy.buyAmount);
      const stepNumber = i + 1;
      this.logger.log(
        `${stepNumber}: buyPrice ${buyPrice} | sellPrice ${sellPrice} | amount ${amount}`,
      );
      await this.stepsService.create({
        productId: strategy.productId,
        stepNumber,
        buyPrice: Number(buyPrice),
        sellPrice: Number(sellPrice),
        amount: amount,
      });
    }
  }

  async create(createStrategyDto: CreateStrategyDto) {
    const strategy = this.strategiesRepository.create(createStrategyDto);
    const savedStrategy = await this.strategiesRepository.save(strategy);
    const strategyWithProduct = await this.strategiesRepository.findOne({
      where: { id: savedStrategy.id },
      relations: ["product"],
    });
    if (strategyWithProduct) {
      await this.generateSteps(strategyWithProduct);
    }
    return strategyWithProduct;
  }

  findAll() {
    return this.strategiesRepository.find({
      relations: ["product"],
    });
  }

  async findOne(id: number) {
    const strategy = await this.strategiesRepository.findOne({
      where: { id },
      relations: ["product"],
    });

    if (!strategy) {
      throw new NotFoundException(`Strategy with ID ${id} not found`);
    }

    return strategy;
  }

  async update(id: number, updateStrategyDto: UpdateStrategyDto) {
    const strategy = await this.findOne(id);
    Object.assign(strategy, updateStrategyDto);
    return this.strategiesRepository.save(strategy);
  }

  async remove(id: number) {
    const strategy = await this.findOne(id);
    return this.strategiesRepository.remove(strategy);
  }

  async updateSteps(id: number) {
    const strategy = await this.findOne(id);
    await this.generateSteps(strategy);
    return strategy;
  }
}
