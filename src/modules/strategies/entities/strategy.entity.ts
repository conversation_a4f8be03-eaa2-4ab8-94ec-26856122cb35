import { Column, <PERSON><PERSON>ty, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Product } from "../../products/entities/product.entity";

export enum ExchangeType {
  KRAKEN = "kraken",
  KUCOIN = "kucoin",
}

@Entity("strategies")
export class Strategy {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  buyAmount!: number;

  @Column({ type: "decimal", precision: 5, scale: 2 })
  profitPercentage!: number;

  @Column({ type: "enum", enum: ExchangeType })
  exchange!: ExchangeType;

  @ManyToOne(() => Product)
  product!: Product;

  @Column()
  productId!: number;

  @Column({ default: true })
  isActive: boolean = true;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
