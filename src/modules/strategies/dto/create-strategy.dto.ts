import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ot<PERSON>mpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { ExchangeType } from "../entities/strategy.entity";

export class CreateStrategyDto {
  @IsString({ message: "Name must be a string" })
  @IsNotEmpty({ message: "Name is required" })
  name!: string;

  @IsNumber({}, { message: "Buy amount must be a number" })
  @IsPositive({ message: "Buy amount must be a positive number" })
  buyAmount!: number;

  @IsNumber({}, { message: "Profit percentage must be a number" })
  @IsPositive({ message: "Profit percentage must be a positive number" })
  @Min(0.01, { message: "Profit percentage must be at least 0.01%" })
  @Max(100, { message: "Profit percentage cannot exceed 100%" })
  profitPercentage!: number;

  @IsEnum(ExchangeType, { message: "Exchange must be a valid exchange type" })
  exchange!: ExchangeType;

  @IsNumber({}, { message: "Product ID must be a number" })
  @IsPositive({ message: "Product ID must be a positive number" })
  productId!: number;
}
