import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { ExchangeType } from "../entities/strategy.entity";

export class UpdateStrategyDto {
  @IsString({ message: "Name must be a string" })
  @IsOptional()
  name?: string;

  @IsNumber({}, { message: "Buy amount must be a number" })
  @IsPositive({ message: "Buy amount must be a positive number" })
  @IsOptional()
  buyAmount?: number;

  @IsNumber({}, { message: "Profit percentage must be a number" })
  @IsPositive({ message: "Profit percentage must be a positive number" })
  @Min(0.01, { message: "Profit percentage must be at least 0.01%" })
  @Max(100, { message: "Profit percentage cannot exceed 100%" })
  @IsOptional()
  profitPercentage?: number;

  @IsEnum(ExchangeType, { message: "Exchange must be a valid exchange type" })
  @IsOptional()
  exchange?: ExchangeType;

  @IsNumber({}, { message: "Product ID must be a number" })
  @IsPositive({ message: "Product ID must be a positive number" })
  @IsOptional()
  productId?: number;

  @IsOptional()
  isActive?: boolean;
}
