import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from "@nestjs/common";
import { Roles } from "src/modules/auth/decorators/roles.decorator";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/modules/auth/guards/roles.guard";
import { UserRole } from "src/modules/users/entities/user.entity";
import { CreateStrategyDto } from "./dto/create-strategy.dto";
import { UpdateStrategyDto } from "./dto/update-strategy.dto";
import { StrategiesService } from "./strategies.service";

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller("strategies")
export class StrategiesController {
  constructor(private readonly strategiesService: StrategiesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  create(@Body() createStrategyDto: CreateStrategyDto) {
    return this.strategiesService.create(createStrategyDto);
  }

  @Get()
  findAll() {
    return this.strategiesService.findAll();
  }

  @Get(":id")
  @Roles(UserRole.USER)
  findOne(@Param("id") id: string) {
    return this.strategiesService.findOne(+id);
  }

  @Put(":id")
  @Roles(UserRole.ADMIN)
  update(@Param("id") id: string, @Body() updateStrategyDto: UpdateStrategyDto) {
    return this.strategiesService.update(+id, updateStrategyDto);
  }

  @Delete(":id")
  @Roles(UserRole.ADMIN)
  remove(@Param("id") id: string) {
    return this.strategiesService.remove(+id);
  }

  @Post(":id/steps")
  @Roles(UserRole.ADMIN)
  updateSteps(@Param("id") id: string) {
    return this.strategiesService.updateSteps(+id);
  }
}
