import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Req,
  UnauthorizedException,
  UseGuards,
} from "@nestjs/common";
import { Throttle } from "@nestjs/throttler";
import { User } from "../users/entities/user.entity";
import { AuthService } from "./auth.service";
import { CurrentUser } from "./decorators/current-user.decorator";
import { Public } from "./decorators/public.decorator";
import { RegisterDto } from "./dto/register.dto";
import { UpdateProfileDto } from "./dto/update-profile.dto";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import { LocalAuthGuard } from "./guards/local-auth.guard";
import { RefreshTokenGuard } from "./guards/refresh-token.guard";

@Controller("auth")
@Throttle({ default: { limit: 5, ttl: 60 } })
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @UseGuards(LocalAuthGuard)
  @Post("login")
  async login(@CurrentUser() user: Partial<User>) {
    return this.authService.login(user);
  }

  @Public()
  @Post("register")
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Public()
  @Post("verify-email")
  async verifyEmail(@Body() body: { email: string; code: string }) {
    return this.authService.verifyEmail(body.email, body.code);
  }

  @Public()
  @Post("resend-verification")
  async resendVerification(@Body() body: { email: string }) {
    return this.authService.resendVerificationCode(body.email);
  }

  @UseGuards(RefreshTokenGuard)
  @Post("refresh")
  async refresh(@Req() req: any) {
    return this.authService.refreshTokens(req.user);
  }

  @UseGuards(JwtAuthGuard)
  @Post("logout")
  async logout(@CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.authService.logout(user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Get("profile")
  getProfile(@CurrentUser() user: Partial<User>) {
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Put("profile")
  async updateProfile(
    @CurrentUser() user: Partial<User>,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.authService.updateProfile(user.id, updateProfileDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post("verify")
  async verify(@CurrentUser() user: Partial<User>, @Body() body: { code: string }) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.authService.verify(user.id, body.code);
  }
}
