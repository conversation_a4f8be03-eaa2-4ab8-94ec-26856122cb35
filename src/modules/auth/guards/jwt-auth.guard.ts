import { ExecutionContext, Injectable, Logger, UnauthorizedException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthGuard } from "@nestjs/passport";

@Injectable()
export class JwtAuthGuard extends AuthGuard("jwt") {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    this.logger.debug("Checking authentication", {
      path: request.path,
      method: request.method,
      hasAuthHeader: !!request.headers.authorization,
    });

    const isPublic = this.reflector.getAllAndOverride<boolean>("isPublic", [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      this.logger.debug("Route is public, skipping authentication");
      return true;
    }

    try {
      const result = await super.canActivate(context);
      if (!result) {
        this.logger.warn("Authentication failed - no result from parent guard");
        return false;
      }

      const user = request.user;
      if (!user) {
        this.logger.warn("Authentication failed - no user in request");
        throw new UnauthorizedException("No user found in request");
      }

      if (!user.isVerified) {
        this.logger.warn("Authentication failed - user not verified", { userId: user.id });
        throw new UnauthorizedException("Please verify your email first");
      }

      this.logger.debug("Authentication successful", {
        id: user.id,
        email: user.email,
        role: user.role,
      });
      return true;
    } catch (error: any) {
      this.logger.error("Authentication error", {
        error: error.message,
        stack: error.stack,
        path: request.path,
        method: request.method,
      });
      throw error;
    }
  }
}
