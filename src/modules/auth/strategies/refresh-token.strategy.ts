import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PassportStrategy } from "@nestjs/passport";
import { Request } from "express";
import { ExtractJwt, Strategy } from "passport-jwt";

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(Strategy, "jwt-refresh") {
  constructor(private configService: ConfigService) {
    const jwtSecret = configService.get<string>("JWT_REFRESH_SECRET");
    if (!jwtSecret) {
      throw new Error("JWT_REFRESH_SECRET is not defined");
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: jwtSecret,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: any) {
    const refreshToken = req.get("Authorization")?.replace("Bearer", "").trim();
    if (!refreshToken) {
      throw new UnauthorizedException("No refresh token provided");
    }
    return {
      id: payload.sub,
      email: payload.email,
      refreshToken,
      role: payload.role,
    };
  }
}
