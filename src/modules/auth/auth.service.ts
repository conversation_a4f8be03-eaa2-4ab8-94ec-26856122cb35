import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcrypt";
import { randomBytes } from "crypto";
import {
  InvalidCredentialsException,
  UserNotFoundException,
} from "../../common/exceptions/auth.exception";
import { AppLogger } from "../../common/logger/app.logger";
import { EmailService } from "../email/email.service";
import { UsersService } from "../users/users.service";
import { RegisterDto } from "./dto/register.dto";

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
    private readonly logger: AppLogger,
  ) {}

  async validateUser(email: string, pass: string): Promise<any> {
    this.logger.debug(`Attempting to validate user: ${email}`);

    const user = await this.usersService.findByEmail(email);
    if (!user) {
      this.logger.warn(`User not found: ${email}`);
      throw new UserNotFoundException();
    }

    const isPasswordValid = await bcrypt.compare(pass, user.password);
    if (!isPasswordValid) {
      this.logger.warn(`Invalid password for user: ${email}`);
      throw new InvalidCredentialsException();
    }

    this.logger.debug(`User validated successfully: ${email}`);
    const { password, ...result } = user;
    return result;
  }

  async login(user: any) {
    this.logger.debug(`User logging in: ${user.email}`);
    if (!user.isVerified) {
      throw new UnauthorizedException("User is not verified");
    }

    const tokens = await this.getTokens(user.id, user.email, user.isVerified, user.role);
    this.logger.log(`User logged in successfully: ${user.email}`);

    // Return format expected by frontend
    return {
      access_token: tokens.accessToken,
      user: {
        id: user.id,
        email: user.email,
        isVerified: user.isVerified,
        role: user.role,
      },
    };
  }

  async refreshTokens(refreshToken: string) {
    this.logger.debug(`Attempting to refresh tokens with refresh token: ${refreshToken}`);

    if (!refreshToken) {
      this.logger.warn(`Invalid token refresh request: refreshToken is missing`);
      throw new UnauthorizedException("Invalid token refresh request");
    }

    // Verify the refresh token and extract user info
    let payload;
    try {
      payload = await this.jwtService.verifyAsync(refreshToken, {
        secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
      });
    } catch (error) {
      this.logger.warn(`Invalid refresh token`);
      throw new UnauthorizedException("Invalid refresh token");
    }

    const user = await this.usersService.findById(payload.sub);
    if (!user) {
      this.logger.warn(`User not found for token refresh: ${payload.sub}`);
      throw new UserNotFoundException();
    }

    const tokens = await this.getTokens(user.id, user.email, user.isVerified, user.role);
    this.logger.log(`Tokens refreshed successfully for user: ${user.email}`);
    return tokens;
  }

  async logout(userId: number) {
    this.logger.debug(`User logging out: ${userId}`);
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`User not found for logout: ${userId}`);
      throw new UserNotFoundException();
    }
    this.logger.log(`User logged out successfully: ${user.email}`);
  }

  private async getTokens(userId: number, email: string, isVerified: boolean, role: string) {
    this.logger.debug(`Generating tokens for user: ${email} | userId: ${userId}`);
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
          isVerified,
          role,
        },
        {
          secret: this.configService.get<string>("JWT_SECRET"),
          expiresIn: "24h",
        },
      ),
      this.jwtService.signAsync(
        {
          sub: userId,
          email,
          isVerified,
          role,
        },
        {
          secret: this.configService.get<string>("JWT_REFRESH_SECRET"),
          expiresIn: "7d",
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async register(
    registerDto: RegisterDto,
  ): Promise<{ user: any; accessToken: string; refreshToken: string }> {
    this.logger.debug(`Attempting to register new user: ${registerDto.email}`);

    const existingUser = await this.usersService.findByEmail(registerDto.email);
    if (existingUser) {
      this.logger.warn(`Registration failed - User already exists: ${registerDto.email}`);
      throw new UnauthorizedException("User already exists");
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    const newUser = await this.usersService.create({
      email: registerDto.email,
      password: hashedPassword,
      isVerified: true, // Auto-verify users
    });

    this.logger.log(`User registered and auto-verified successfully: ${registerDto.email}`);

    // Generate tokens
    const tokens = await this.generateTokens(newUser);

    // Exclude password from the user object
    const { password, ...userWithoutPassword } = newUser;

    return {
      user: userWithoutPassword,
      ...tokens,
    };
  }

  async verifyEmail(email: string, code: string) {
    this.logger.debug(`Attempting to verify email: ${email}`);

    const user = await this.usersService.findByEmail(email);
    if (!user) {
      this.logger.warn(`Email verification failed - User not found: ${email}`);
      throw new UserNotFoundException();
    }

    if (!user.verificationCode || user.verificationCode !== code) {
      this.logger.warn(`Email verification failed - Invalid code for user: ${email}`);
      this.logger.error(`Invalid code : ${code}`);
      this.logger.error(`Code : ${user.verificationCode}`);
      throw new UnauthorizedException("Invalid verification code");
    }

    if (!user.verificationExpires || user.verificationExpires < new Date()) {
      this.logger.warn(`Email verification failed - Code expired for user: ${email}`);
      throw new UnauthorizedException("Verification code has expired");
    }

    await this.usersService.update(user.id, {
      isVerified: true,
      verificationCode: undefined,
      verificationExpires: undefined,
    });

    const tokens = await this.generateTokens(user);
    this.logger.log(`Email verified successfully: ${email}`);

    // Exclude password from the user object
    const { password, ...userWithoutPassword } = user;
    return { user: userWithoutPassword, ...tokens };
  }

  async resendVerificationCode(email: string): Promise<void> {
    this.logger.debug(`Attempting to resend verification code: ${email}`);

    const user = await this.usersService.findByEmail(email);
    if (!user) {
      this.logger.warn(`Resend verification failed - User not found: ${email}`);
      throw new UserNotFoundException();
    }

    if (user.isVerified) {
      this.logger.warn(`Resend verification failed - Email already verified: ${email}`);
      throw new UnauthorizedException("Email is already verified");
    }

    const verificationCode = randomBytes(3).toString("hex").toUpperCase();
    const verificationExpires = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    await this.usersService.update(user.id, {
      verificationCode,
      verificationExpires,
    });

    await this.emailService.sendVerificationEmail(email, verificationCode);
    this.logger.debug(`Verification code resent successfully: ${email}`);
  }

  private async generateTokens(user: any) {
    this.logger.debug(`Generating tokens for user: ${user.email}`);
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        { sub: user.id, email: user.email },
        {
          secret: this.configService.get("JWT_ACCESS_SECRET"),
          expiresIn: "15m",
        },
      ),
      this.jwtService.signAsync(
        { sub: user.id, email: user.email },
        {
          secret: this.configService.get("JWT_REFRESH_SECRET"),
          expiresIn: "7d",
        },
      ),
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async validateJwtPayload(payload: any) {
    this.logger.debug(`Validating JWT payload for user ID: ${payload.sub}`);
    const user = await this.usersService.findById(payload.sub);
    if (!user) {
      this.logger.warn(`JWT validation failed - User not found: ${payload.sub}`);
      throw new UserNotFoundException();
    }
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async verify(userId: number, code: string) {
    this.logger.debug(`Attempting to verify user: ${userId}`);
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`Verification failed - User not found: ${userId}`);
      throw new UserNotFoundException();
    }
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async updateProfile(userId: number, updateData: any) {
    this.logger.debug(`Updating profile for user: ${userId}`);
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`Profile update failed - User not found: ${userId}`);
      throw new UserNotFoundException();
    }

    const updatedUser = await this.usersService.update(userId, updateData);
    const { password, ...userWithoutPassword } = updatedUser;
    this.logger.log(`Profile updated successfully for user: ${userId}`);
    return userWithoutPassword;
  }
}
