import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { User } from "src/modules/users/entities/user.entity";

export const CurrentUser = createParamDecorator((data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  const user = request.user;
  if (user) {
    const { id, email, role, isVerified } = user;
    return { id, email, role, isVerified };
  }
  return user as User;
});
