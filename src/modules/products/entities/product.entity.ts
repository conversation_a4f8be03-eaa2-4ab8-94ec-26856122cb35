import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("products")
export class Product {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ unique: true })
  symbol!: string; // e.g., BTC, SOL

  @Column()
  name!: string; // e.g., Bitcoin, Solana

  @Column({ type: "decimal", precision: 10, scale: 2 })
  priceUsd!: number;

  @Column({ default: true })
  isActive: boolean = true;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
