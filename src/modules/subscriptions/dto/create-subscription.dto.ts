import { Type } from "class-transformer";
import { IsDate, IsNotEmpty, <PERSON>N<PERSON><PERSON> } from "class-validator";

export class CreateSubscriptionDto {
  @IsNotEmpty()
  @IsNumber()
  strategyId!: number;

  @IsNotEmpty()
  @IsNumber()
  apiKeyId!: number;

  @IsNotEmpty()
  @IsNumber()
  amount!: number;

  @IsNotEmpty()
  @IsNumber()
  paymentId!: number;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  startDate!: Date;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  endDate!: Date;
}
