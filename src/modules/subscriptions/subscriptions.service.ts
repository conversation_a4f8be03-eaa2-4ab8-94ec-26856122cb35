import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { CreateSubscriptionDto } from "./dto/create-subscription.dto";
import { UpdateSubscriptionDto } from "./dto/update-subscription.dto";
import { Subscription, SubscriptionStatus } from "./entities/subscription.entity";

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
  ) {}

  async create(
    createSubscriptionDto: CreateSubscriptionDto,
    userId: number,
  ): Promise<Subscription> {
    this.logger.debug("Starting subscription creation", {
      dto: createSubscriptionDto,
      userId,
    });

    try {
      // Validate that referenced entities exist
      const [strategy, apiKey, payment] = await Promise.all([
        this.subscriptionRepository.query("SELECT id FROM strategies WHERE id = $1", [
          createSubscriptionDto.strategyId,
        ]),
        this.subscriptionRepository.query("SELECT id FROM api_keys WHERE id = $1", [
          createSubscriptionDto.apiKeyId,
        ]),
        this.subscriptionRepository.query("SELECT id FROM payments WHERE id = $1", [
          createSubscriptionDto.paymentId,
        ]),
      ]);

      this.logger.debug("Referenced entities check", {
        strategyExists: strategy.length > 0,
        apiKeyExists: apiKey.length > 0,
        paymentExists: payment.length > 0,
      });

      if (!strategy.length || !apiKey.length || !payment.length) {
        const missing = [];
        if (!strategy.length) missing.push("strategy");
        if (!apiKey.length) missing.push("apiKey");
        if (!payment.length) missing.push("payment");
        throw new NotFoundException(`Referenced entities not found: ${missing.join(", ")}`);
      }

      const subscription = this.subscriptionRepository.create({
        ...createSubscriptionDto,
        userId,
      });

      this.logger.debug("Created subscription entity", { subscription });

      const savedSubscription = await this.subscriptionRepository.save(subscription);
      this.logger.debug("Saved subscription", { savedSubscription });

      return savedSubscription;
    } catch (error: any) {
      this.logger.error("Error creating subscription", {
        error: error.message,
        stack: error.stack,
        code: error.code,
        detail: error.detail,
      });
      throw error;
    }
  }

  async findAll(userId: number): Promise<Subscription[]> {
    this.logger.debug("Starting to fetch subscriptions", { userId });

    if (!userId || typeof userId !== "number") {
      this.logger.error("Invalid userId provided", { userId, type: typeof userId });
      throw new Error("Invalid userId provided");
    }

    try {
      this.logger.debug("Executing database query", { userId });
      const subscriptions = await this.subscriptionRepository.find({
        where: { userId },
        relations: ["strategy", "apiKey", "payment", "strategy.product"],
        order: { createdAt: "DESC" },
      });

      if (!Array.isArray(subscriptions)) {
        this.logger.error("Database returned non-array result", {
          result: subscriptions,
          type: typeof subscriptions,
        });
        throw new Error("Invalid database response");
      }

      this.logger.debug("Successfully fetched subscriptions", {
        userId,
        count: subscriptions.length,
        subscriptionIds: subscriptions.map(s => s.id),
      });

      return subscriptions;
    } catch (error: any) {
      this.logger.error("Error fetching subscriptions", {
        error: error.message,
        stack: error.stack,
        code: error.code,
        detail: error.detail,
        userId,
        errorType: error.constructor.name,
        query: error.query, // Log the failed query if available
      });

      // Re-throw the error to be handled by the controller
      throw error;
    }
  }

  async findOne(id: number, userId: number): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id, userId },
      relations: ["strategy", "apiKey", "payment", "strategy.product"],
    });
    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }
    return subscription;
  }

  async update(
    id: number,
    updateSubscriptionDto: UpdateSubscriptionDto,
    userId: number,
  ): Promise<Subscription> {
    const subscription = await this.findOne(id, userId);
    Object.assign(subscription, updateSubscriptionDto);
    return this.subscriptionRepository.save(subscription);
  }

  async remove(id: number, userId: number): Promise<void> {
    const subscription = await this.findOne(id, userId);
    await this.subscriptionRepository.remove(subscription);
  }

  async findAllActive(): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
      },
      relations: ["strategy", "apiKey", "payment", "strategy.product"],
      order: { createdAt: "DESC" },
    });
  }

  async findActiveBySymbol(symbol: string): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
        strategy: {
          product: {
            symbol,
          },
        },
      },
      relations: ["strategy", "strategy.product", "apiKey", "payment"],
      order: { createdAt: "DESC" },
    });
  }
}
