import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Inject,
  Logger,
  Param,
  Post,
  Put,
  Req,
  UnauthorizedException,
  UseGuards,
  forwardRef,
} from "@nestjs/common";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { RolesGuard } from "src/modules/auth/guards/roles.guard";
import { User } from "src/modules/users/entities/user.entity";
import { ExchangesService } from "../exchanges/exchanges.service";
import { CreateSubscriptionDto } from "./dto/create-subscription.dto";
import { UpdateSubscriptionDto } from "./dto/update-subscription.dto";
import { SubscriptionsService } from "./subscriptions.service";

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller("subscriptions")
export class SubscriptionsController {
  private readonly logger = new Logger(SubscriptionsController.name);

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    @Inject(forwardRef(() => ExchangesService))
    private readonly exchangesService: ExchangesService,
  ) {}

  @Post()
  async create(
    @Body() createSubscriptionDto: CreateSubscriptionDto,
    @CurrentUser() user: Partial<User>,
  ) {
    this.logger.debug("Received subscription creation request", {
      dto: JSON.stringify(createSubscriptionDto, null, 2),
      userId: user.id,
    });

    // Validate required fields
    const requiredFields = ["strategyId", "apiKeyId", "paymentId", "startDate", "endDate"];
    const missingFields = requiredFields.filter(
      field => !createSubscriptionDto[field as keyof CreateSubscriptionDto],
    );

    if (missingFields.length > 0) {
      this.logger.warn("Missing required fields", { missingFields });
      throw new HttpException(
        {
          message: "Validation failed",
          details: missingFields.map(field => `${field} is required`),
          error: "VALIDATION_ERROR",
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      if (!user.id) {
        throw new UnauthorizedException("User not found");
      }
      const result = await this.subscriptionsService.create(createSubscriptionDto, user.id);
      this.logger.debug("Successfully created subscription", { subscriptionId: result.id });
      return result;
    } catch (error: any) {
      this.logger.error("Failed to create subscription", {
        error: error.message,
        stack: error.stack,
        code: error.code,
        detail: error.detail,
        dto: createSubscriptionDto,
        userId: user.id,
      });

      if (error instanceof HttpException) {
        throw error;
      }

      // Check for specific database errors
      if (error.code === "23503") {
        throw new HttpException(
          {
            message: "One or more referenced entities (strategy, apiKey, or payment) do not exist",
            error: "FOREIGN_KEY_VIOLATION",
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (error.code === "23505") {
        throw new HttpException(
          {
            message: "A subscription with these details already exists",
            error: "UNIQUE_VIOLATION",
          },
          HttpStatus.CONFLICT,
        );
      }

      throw new HttpException(
        {
          message: `Failed to create subscription: ${error.message || "Unknown error"}`,
          error: "INTERNAL_SERVER_ERROR",
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(@CurrentUser() user: Partial<User>) {
    this.logger.debug("Fetching subscriptions for user", { userId: user.id });

    if (!user || !user.id) {
      this.logger.error("Invalid user object", { user });
      throw new HttpException(
        {
          message: "Invalid user object",
          error: "INVALID_USER",
          details: ["User object is missing or invalid"],
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    try {
      this.logger.debug("Calling service.findAll", { userId: user.id });
      const subscriptions = await this.subscriptionsService.findAll(user.id);

      if (!Array.isArray(subscriptions)) {
        this.logger.error("Service returned non-array result", {
          result: subscriptions,
          type: typeof subscriptions,
        });
        throw new HttpException(
          {
            message: "Invalid service response",
            error: "INVALID_RESPONSE",
            details: ["Service returned invalid response type"],
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      this.logger.debug("Successfully fetched subscriptions", {
        count: subscriptions.length,
        userId: user.id,
      });
      return subscriptions;
    } catch (error: any) {
      this.logger.error("Failed to fetch subscriptions", {
        error: error.message,
        stack: error.stack,
        code: error.code,
        detail: error.detail,
        userId: user.id,
        errorType: error.constructor.name,
      });

      if (error instanceof HttpException) {
        throw error;
      }

      // Handle specific database errors
      if (error.code === "23503") {
        throw new HttpException(
          {
            message: "Database foreign key violation",
            error: "FOREIGN_KEY_VIOLATION",
            details: [error.detail || "Referenced entity does not exist"],
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      throw new HttpException(
        {
          message: "Failed to fetch subscriptions",
          error: "INTERNAL_SERVER_ERROR",
          details: [error.message || "Unknown error"],
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(":id")
  findOne(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.subscriptionsService.findOne(+id, user.id);
  }

  @Put(":id")
  update(
    @Param("id") id: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
    @CurrentUser() user: Partial<User>,
  ) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.subscriptionsService.update(+id, updateSubscriptionDto, user.id);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.subscriptionsService.remove(+id, user.id);
  }

  @Get(":id/details")
  async getSubscriptionDetails(
    @Param("id") id: string,
    @CurrentUser() user: Partial<User>,
    @Req() req: any,
  ) {
    this.logger.debug("Fetching subscription details", { subscriptionId: id, userId: user.id });

    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }

    try {
      const subscription = await this.subscriptionsService.findOne(+id, user.id);

      // Get open orders using the subscription's API key and exchange
      const openOrders = await this.exchangesService.getOpenOrders(
        subscription.apiKey.apiKey,
        subscription.apiKey.apiSecret,
        subscription.apiKey.exchange,
      );

      const response = {
        success: true,
        data: [
          {
            ...subscription,
            status: this.determineSubscriptionStatus(subscription),
            openOrders,
          },
        ],
        meta: {
          timestamp: new Date().toISOString(),
          path: req.path,
          method: req.method,
        },
      };

      this.logger.debug("Successfully fetched subscription details", {
        subscriptionId: id,
        userId: user.id,
      });

      return response;
    } catch (error: any) {
      this.logger.error("Failed to fetch subscription details", {
        error: error.message,
        stack: error.stack,
        subscriptionId: id,
        userId: user.id,
      });

      throw new HttpException(
        {
          message: "Failed to fetch subscription details",
          error: "INTERNAL_SERVER_ERROR",
          details: [error.message || "Unknown error"],
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(":id/orders")
  async getSubscriptionOrders(
    @Param("id") id: string,
    @CurrentUser() user: Partial<User>,
    @Req() req: any,
  ) {
    this.logger.debug("Fetching open orders for subscription", {
      subscriptionId: id,
      userId: user.id,
    });
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }

    try {
      const subscription = await this.subscriptionsService.findOne(+id, user.id);

      const KRAKEN_API_KEY = "*********************************************************;
      const KRAKEN_API_SECRET =
        "gwDK0VscoILEqTEhiMhqvAbV9yDZjPqIoz8wTREIW0ZxmBCD4tm4SaK8Kny0sNzczubt12/3FdLsCEPhn6r23Q==";

      // Get open orders using the subscription's API key and exchange
      const openOrders = await this.exchangesService.getOpenOrders(
        // subscription.apiKey.apiKey,
        // subscription.apiKey.apiSecret,
        // subscription.apiKey.exchange,
        KRAKEN_API_KEY,
        KRAKEN_API_SECRET,
        "kraken",
      );

      const response = {
        success: true,
        data: {
          subscription: {
            id: subscription.id,
            status: this.determineSubscriptionStatus(subscription),
            strategy: subscription.strategy,
            apiKey: {
              id: subscription.apiKey.id,
              name: subscription.apiKey.name,
              exchange: subscription.apiKey.exchange,
            },
          },
          orders: openOrders,
        },
        meta: {
          timestamp: new Date().toISOString(),
          path: req.path,
          method: req.method,
        },
      };

      this.logger.debug("Successfully fetched subscription orders", {
        subscriptionId: id,
        userId: user.id,
        orderCount: openOrders.length,
      });

      return response;
    } catch (error: any) {
      this.logger.error("Failed to fetch subscription orders", {
        error: error.message,
        stack: error.stack,
        subscriptionId: id,
        userId: user.id,
      });

      throw new HttpException(
        {
          message: "Failed to fetch subscription orders",
          error: "INTERNAL_SERVER_ERROR",
          details: [error.message || "Unknown error"],
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private determineSubscriptionStatus(subscription: any): string {
    const now = new Date();
    const startDate = new Date(subscription.startDate);
    const endDate = new Date(subscription.endDate);

    if (now < startDate) return "pending";
    if (now > endDate) return "expired";
    return "active";
  }
}
