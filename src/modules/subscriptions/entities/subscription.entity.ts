import { Column, <PERSON>tity, <PERSON>ToOne, PrimaryGeneratedColumn } from "typeorm";
import { ApiKey } from "../../api-keys/entities/api-key.entity";
import { Payment } from "../../payments/entities/payment.entity";
import { Strategy } from "../../strategies/entities/strategy.entity";
import { User } from "../../users/entities/user.entity";

export enum SubscriptionStatus {
  ACTIVE = "active",
  PAUSED = "paused",
  CANCELLED = "cancelled",
  EXPIRED = "expired",
}

@Entity("subscriptions")
export class Subscription {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => User)
  user!: User;

  @Column()
  userId!: number;

  @Column({ type: "decimal", precision: 20, scale: 8 })
  amount!: number;

  @ManyToOne(() => Strategy)
  strategy!: Strategy;

  @Column()
  strategyId!: number;

  @ManyToOne(() => ApiKey)
  apiKey!: ApiKey;

  @Column()
  apiKeyId!: number;

  @ManyToOne(() => Payment)
  payment!: Payment;

  @Column()
  paymentId!: number;

  @Column({ type: "timestamp" })
  startDate!: Date;

  @Column({ type: "timestamp" })
  endDate!: Date;

  @Column({
    type: "enum",
    enum: SubscriptionStatus,
    default: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus = SubscriptionStatus.ACTIVE;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
