import { ExchangeType } from "src/modules/strategies/entities/strategy.entity";
import { User } from "src/modules/users/entities/user.entity";
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

@Entity("api_keys")
export class ApiKey {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column()
  name!: string;

  @Column({ type: "enum", enum: ExchangeType })
  exchange!: ExchangeType;

  @Column()
  apiKey!: string;

  @Column()
  apiSecret!: string;

  @Column({ default: true })
  isActive: boolean = true;

  @ManyToOne(() => User)
  user!: User;

  @Column()
  userId!: number;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt!: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP", onUpdate: "CURRENT_TIMESTAMP" })
  updatedAt!: Date;
}
