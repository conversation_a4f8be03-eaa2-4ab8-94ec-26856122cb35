import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Api<PERSON>eysController } from "src/modules/api-keys/api-keys.controller";
import { ApiKeysService } from "src/modules/api-keys/api-keys.service";
import { Api<PERSON>ey } from "./entities/api-key.entity";

@Module({
  imports: [TypeOrmModule.forFeature([ApiKey])],
  controllers: [ApiKeysController],
  providers: [ApiKeysService],
  exports: [ApiKeysService],
})
export class ApiKeysModule {}
