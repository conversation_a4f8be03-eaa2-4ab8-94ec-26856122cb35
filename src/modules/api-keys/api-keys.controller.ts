import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UnauthorizedException,
  UseGuards,
} from "@nestjs/common";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { User } from "src/modules/users/entities/user.entity";
import { ApiKeysService } from "./api-keys.service";
import { CreateApiKeyDto } from "./dto/create-api-key.dto";
import { UpdateApiKeyDto } from "./dto/update-api-key.dto";

@UseGuards(JwtAuthGuard)
@Controller("api-keys")
export class ApiKeysController {
  constructor(private readonly apiKeysService: ApiKeysService) {}

  @Post()
  create(@Body() createApiKeyDto: CreateApiKeyDto, @CurrentUser() user: Partial<User>) {
    if (!user.id) {
      throw new UnauthorizedException("User not found");
    }
    return this.apiKeysService.create(createApiKeyDto, user);
  }

  @Get()
  findAll(@CurrentUser() user: Partial<User>) {
    return this.apiKeysService.findAll(user);
  }

  @Get(":id")
  findOne(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    return this.apiKeysService.findOne(+id, user);
  }

  @Put(":id")
  update(
    @Param("id") id: string,
    @Body() updateApiKeyDto: UpdateApiKeyDto,
    @CurrentUser() user: Partial<User>,
  ) {
    return this.apiKeysService.update(+id, updateApiKeyDto, user);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @CurrentUser() user: Partial<User>) {
    return this.apiKeysService.remove(+id, user);
  }
}
