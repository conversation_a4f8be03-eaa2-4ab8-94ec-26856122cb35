import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { User } from "../users/entities/user.entity";
import { CreateApiKeyDto } from "./dto/create-api-key.dto";
import { UpdateApiKeyDto } from "./dto/update-api-key.dto";
import { ApiKey } from "./entities/api-key.entity";

@Injectable()
export class ApiKeysService {
  constructor(
    @InjectRepository(ApiKey)
    private apiKeysRepository: Repository<ApiKey>,
  ) {}

  create(createApiKeyDto: CreateApiKeyDto, user: Partial<User>) {
    const apiKey = this.apiKeysRepository.create({
      ...createApiKeyDto,
      user,
    });
    return this.apiKeysRepository.save(apiKey);
  }

  findAll(user: Partial<User>) {
    return this.apiKeysRepository.find({
      where: { user: { id: user.id } },
    });
  }

  async findOne(id: number, user: Partial<User>) {
    const apiKey = await this.apiKeysRepository.findOne({
      where: { id, user: { id: user.id } },
    });

    if (!apiKey) {
      throw new NotFoundException(`API Key with ID ${id} not found`);
    }

    return apiKey;
  }

  async update(id: number, updateApiKeyDto: UpdateApiKeyDto, user: Partial<User>) {
    const apiKey = await this.findOne(id, user);
    Object.assign(apiKey, updateApiKeyDto);
    return this.apiKeysRepository.save(apiKey);
  }

  async remove(id: number, user: Partial<User>) {
    const apiKey = await this.findOne(id, user);
    return this.apiKeysRepository.remove(apiKey);
  }
}
