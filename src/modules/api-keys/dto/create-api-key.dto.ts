import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Enum, IsNotEmpty, IsString } from "class-validator";
import { ExchangeType } from "src/modules/strategies/entities/strategy.entity";

export class CreateApiKeyDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The name of the API key" })
  name!: string;

  @IsEnum(ExchangeType)
  @ApiProperty({ description: "The exchange type of the API key" })
  exchange!: ExchangeType;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The API key" })
  apiKey!: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: "The API secret" })
  apiSecret!: string;
}
