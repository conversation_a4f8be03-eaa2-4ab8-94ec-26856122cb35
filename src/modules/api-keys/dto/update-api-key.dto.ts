import { IsEnum, IsOptional, IsString } from "class-validator";
import { ExchangeType } from "src/modules/strategies/entities/strategy.entity";

export class UpdateApiKeyDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(ExchangeType)
  @IsOptional()
  exchange?: ExchangeType;

  @IsString()
  @IsOptional()
  apiKey?: string;

  @IsString()
  @IsOptional()
  apiSecret?: string;

  @IsOptional()
  isActive?: boolean;
}
