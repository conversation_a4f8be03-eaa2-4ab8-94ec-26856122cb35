import { Logger } from "@nestjs/common";
import dataSource from "../config/data-source";

const logger = new Logger("DatabaseDrop");

async function dropDatabase() {
  try {
    // Initialize the data source if not initialized
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }

    // Drop all tables
    await dataSource.dropDatabase();
    logger.log("Successfully dropped all tables");

    // Close the connection
    await dataSource.destroy();
    process.exit(0);
  } catch (error) {
    logger.error("Error dropping database:", error);
    process.exit(1);
  }
}

dropDatabase();
