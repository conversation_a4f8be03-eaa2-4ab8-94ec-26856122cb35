import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { APP_GUARD } from "@nestjs/core";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { ThrottlerGuard, ThrottlerModule } from "@nestjs/throttler";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ExchangesModule } from "src/modules/exchanges/exchanges.module";
import { ProductsModule } from "src/modules/products/products.module";
import { StrategiesModule } from "src/modules/strategies/strategies.module";
import { VerificationModule } from "src/modules/verification/verification.module";
import { LoggerModule } from "./common/logger/logger.module";
import { AdminModule } from "./modules/admin/admin.module";
import { ApiKeysModule } from "./modules/api-keys/api-keys.module";
import { AuthModule } from "./modules/auth/auth.module";
import { PaymentsModule } from "./modules/payments/payments.module";
import { SubscriptionsModule } from "./modules/subscriptions/subscriptions.module";
import { UsersModule } from "./modules/users/users.module";

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ThrottlerModule.forRoot([
      {
        ttl: 60,
        limit: 10,
      },
    ]),
    TypeOrmModule.forRoot({
      type: "postgres",
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || "5432", 10),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      entities: [__dirname + "/**/*.entity{.ts,.js}"],
      synchronize: process.env.NODE_ENV !== "production",
    }),
    AuthModule,
    UsersModule,
    ExchangesModule,
    StrategiesModule,
    AdminModule,
    ApiKeysModule,
    PaymentsModule,
    SubscriptionsModule,
    ProductsModule,
    VerificationModule,
    LoggerModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}
