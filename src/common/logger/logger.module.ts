import { Global, Module } from "@nestjs/common";
import { AppLogger } from "./app.logger";
import { createLogger } from "./logger.factory";

@Global()
@Module({
  providers: [
    {
      provide: "winston",
      useFactory: () => createLogger("PingPong"),
    },
    {
      provide: AppLogger,
      useFactory: logger => new AppLogger(logger),
      inject: ["winston"],
    },
  ],
  exports: [AppLogger],
})
export class LoggerModule {}
