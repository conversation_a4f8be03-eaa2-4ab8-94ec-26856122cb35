import { WinstonModule, utilities as nestWinstonModuleUtilities } from "nest-winston";
import { format, transports } from "winston";

export const createLogger = (appName: string) => {
  const isJsonFormat = process.env.USE_JSON_LOGGER === "true";

  const consoleFormat = isJsonFormat
    ? format.combine(format.timestamp(), format.ms(), format.json())
    : format.combine(
        format.timestamp(),
        format.ms(),
        nestWinstonModuleUtilities.format.nestLike(appName, {
          colors: true,
          prettyPrint: true,
        }),
      );

  return WinstonModule.createLogger({
    level: process.env.LOG_LEVEL || "info",
    transports: [
      new transports.Console({
        format: consoleFormat,
      }),
    ],
  });
};
