import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { Request, Response } from "express";

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse();

    let errorResponse;

    // If the exception response is already in our format, use it directly
    if (
      typeof exceptionResponse === "object" &&
      "status" in exceptionResponse &&
      "error" in exceptionResponse
    ) {
      errorResponse = exceptionResponse;
    } else {
      errorResponse = {
        status: "error",
        data: null,
        error: {
          code: this.getErrorCode(status),
          message: this.getErrorMessage(exceptionResponse),
          details: this.getErrorDetails(exceptionResponse),
        },
      };
    }

    // Log the error
    this.logger.error(`${request.method} ${request.url}`, exception.stack, "ExceptionFilter");

    // Store error in response locals for logging
    response.locals.error = errorResponse;

    // For validation errors, use 400 status code
    const responseStatus = this.isValidationError(exceptionResponse)
      ? HttpStatus.BAD_REQUEST
      : status;

    // Send the response
    response.status(responseStatus).json(errorResponse);
  }

  private isValidationError(exceptionResponse: any): boolean {
    if (typeof exceptionResponse === "object") {
      return (
        exceptionResponse.errors?.some((error: any) => error.constraints) ||
        (exceptionResponse.message && Array.isArray(exceptionResponse.message))
      );
    }
    return false;
  }

  private getErrorCode(status: number): string {
    const errorCodes = {
      [HttpStatus.BAD_REQUEST]: "BAD_REQUEST",
      [HttpStatus.UNAUTHORIZED]: "UNAUTHORIZED",
      [HttpStatus.FORBIDDEN]: "FORBIDDEN",
      [HttpStatus.NOT_FOUND]: "NOT_FOUND",
      [HttpStatus.CONFLICT]: "CONFLICT",
      [HttpStatus.UNPROCESSABLE_ENTITY]: "VALIDATION_ERROR",
      [HttpStatus.TOO_MANY_REQUESTS]: "RATE_LIMIT_EXCEEDED",
      [HttpStatus.INTERNAL_SERVER_ERROR]: "INTERNAL_SERVER_ERROR",
    };

    return errorCodes[status as keyof typeof errorCodes] || "UNKNOWN_ERROR";
  }

  private getErrorMessage(exceptionResponse: any): string {
    if (typeof exceptionResponse === "string") {
      return exceptionResponse;
    }

    if (typeof exceptionResponse === "object") {
      if (exceptionResponse.message) {
        return Array.isArray(exceptionResponse.message)
          ? exceptionResponse.message[0]
          : exceptionResponse.message;
      }
    }

    return "An error occurred";
  }

  private getErrorDetails(exceptionResponse: any): any {
    if (typeof exceptionResponse === "object") {
      // Handle validation errors
      if (exceptionResponse.errors) {
        const errors = exceptionResponse.errors.map((error: any) => ({
          field: error.property || error.field,
          messages: error.constraints ? Object.values(error.constraints) : error.messages,
          value: error.value,
          type: "validation_error",
        }));

        const missingFields = errors
          .filter((error: any) =>
            error.messages.some(
              (msg: string) =>
                msg.includes("should not be empty") ||
                msg.includes("must be a string") ||
                msg.includes("must be a valid enum value"),
            ),
          )
          .map((error: any) => error.field);

        return {
          errors,
          missingFields: missingFields.length > 0 ? missingFields : undefined,
        };
      }

      // Handle validation errors from message array
      if (exceptionResponse.message && Array.isArray(exceptionResponse.message)) {
        const errors = exceptionResponse.message.map((error: string) => {
          // Check if the error is about a missing field
          const missingFieldMatch = error.match(/^([a-zA-Z0-9_]+) should not be empty$/);
          if (missingFieldMatch) {
            return {
              field: missingFieldMatch[1],
              message: `${missingFieldMatch[1]} is required`,
              type: "missing_field",
            };
          }
          return {
            message: error,
            type: "validation_error",
          };
        });

        const missingFields = errors
          .filter((error: any) => error.type === "missing_field")
          .map((error: any) => error.field);

        return {
          errors,
          missingFields: missingFields.length > 0 ? missingFields : undefined,
        };
      }

      // Handle custom error details
      if (exceptionResponse.details) {
        return exceptionResponse.details;
      }
    }
    return null;
  }
}
