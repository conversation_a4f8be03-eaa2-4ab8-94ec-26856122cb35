import { ThrottlerOptions } from "@nestjs/throttler";

export const throttleConfig: ThrottlerOptions[] = [
  {
    name: "default",
    ttl: 60,
    limit: 100,
  },
  {
    name: "auth",
    ttl: 60,
    limit: 5, // Stricter limit for auth endpoints
  },
  {
    name: "api",
    ttl: 60,
    limit: 30, // Moderate limit for API endpoints
  },
  {
    name: "admin",
    ttl: 60,
    limit: 50, // Higher limit for admin endpoints
  },
];
