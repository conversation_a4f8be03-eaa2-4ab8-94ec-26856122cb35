import { Injectable } from "@nestjs/common";

@Injectable()
export class ConfigService {
  private readonly envConfig: Record<string, string | undefined>;

  constructor() {
    this.envConfig = process.env;
  }

  get(key: string, defaultValue?: string): string {
    const value = this.envConfig[key];
    if (value === undefined && defaultValue === undefined) {
      throw new Error(`Configuration key "${key}" not found`);
    }
    return value !== undefined ? value : (defaultValue as string);
  }

  getNumber(key: string, defaultValue?: number): number {
    const value = this.get(key, defaultValue?.toString());
    const parsed = Number(value);
    if (isNaN(parsed)) {
      throw new Error(`Configuration key "${key}" is not a number`);
    }
    return parsed;
  }

  getBoolean(key: string, defaultValue?: boolean): boolean {
    const value = this.get(key, defaultValue?.toString());
    return value.toLowerCase() === "true";
  }
}
