export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    path: string;
    method: string;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  meta: {
    timestamp: string;
    path: string;
    method: string;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}
