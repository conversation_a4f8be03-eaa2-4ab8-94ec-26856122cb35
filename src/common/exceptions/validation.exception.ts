import { HttpException, HttpStatus } from "@nestjs/common";

export class ValidationException extends HttpException {
  constructor(errors: any[]) {
    const formattedErrors = errors.map(error => ({
      field: error.property,
      messages: error.constraints
        ? Object.values(error.constraints)
        : [`${error.property} validation failed`],
      value: error.value,
    }));

    super(
      {
        statusCode: HttpStatus.BAD_REQUEST,
        message: "Validation failed",
        errors: formattedErrors,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}
