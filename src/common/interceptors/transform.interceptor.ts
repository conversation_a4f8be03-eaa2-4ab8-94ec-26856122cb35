import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

export interface Response<T> {
  status: "success" | "error";
  data: T | null;
  error: {
    code: string;
    message: string;
    details?: any;
  } | null;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const request = context.switchToHttp().getRequest();
    const { path, method } = request;

    return next.handle().pipe(
      map(data => ({
        status: "success",
        data,
        error: null,
        meta: {
          timestamp: new Date().toISOString(),
          path,
          method,
        },
      })),
    );
  }
}
