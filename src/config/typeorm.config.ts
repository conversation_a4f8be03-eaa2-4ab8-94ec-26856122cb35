// import { ConfigService } from "@nestjs/config";
// import { config } from "dotenv";
// import { DataSource, DataSourceOptions } from "typeorm";

// config();

// const configService = new ConfigService();

// export const dataSourceOptions: DataSourceOptions = {
//   type: "postgres",
//   host: configService.get("DB_HOST"),
//   port: configService.get("DB_PORT"),
//   username: configService.get("DB_USERNAME"),
//   password: configService.get("DB_PASSWORD"),
//   database: configService.get("DB_DATABASE"),
//   entities: ["dist/**/*.entity{.ts,.js}"],
//   migrations: ["dist/migrations/*{.ts,.js}"],
//   synchronize: false,
//   migrationsRun: true,
//   migrationsTableName: "migrations",
//   logging: false,
// };

// const dataSource = new DataSource(dataSourceOptions);
// export default dataSource;
