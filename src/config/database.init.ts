import { Logger } from "@nestjs/common";
import dataSource from "./data-source";

const logger = new Logger("DatabaseInit");

export async function initializeDatabase() {
  try {
    // Initialize the data source
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }

    // Run migrations
    const pendingMigrations = await dataSource.showMigrations();
    if (pendingMigrations) {
      logger.log("Running pending migrations...");
      await dataSource.runMigrations();
      logger.log("Migrations completed successfully");
    } else {
      logger.log("No pending migrations");
    }
  } catch (error) {
    logger.error("Error during database initialization:", error);
    throw error;
  }
}
