# PingPong - Crypto Auto-Trading Platform

PingPong is a sophisticated cryptocurrency auto-trading platform built with NestJS, providing automated trading strategies, exchange integrations, and user management features.

## Features

- 🔐 **Authentication & Authorization**

  - JWT-based authentication
  - Role-based access control (Admin, User)
  - Email verification system
  - Refresh token mechanism

- 💱 **Exchange Integration**

  - Kraken exchange integration
  - Real-time market data
  - Order management
  - Balance tracking
  - Rate limiting for API calls

- 🤖 **Trading Strategies**

  - Automated trading strategies
  - Strategy management
  - Performance tracking
  - Strategy customization

- 👥 **User Management**

  - User registration and profile management
  - API key management
  - Subscription system
  - Role-based permissions

- 📊 **Admin Dashboard**
  - User management
  - Strategy oversight
  - System monitoring
  - Configuration management

## Tech Stack

- **Framework**: NestJS
- **Database**: PostgreSQL with TypeORM
- **Authentication**: Passport.js, JWT
- **API Documentation**: Swagger/OpenAPI
- **Email**: Nodemailer with Handlebars templates
- **Logging**: Winston
- **Testing**: Jest
- **Containerization**: Docker

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL
- Docker (optional)

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/yourusername/pingpong.git
   cd pingpong
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Set up environment variables:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your configuration.

4. Start the database:

   ```bash
   docker-compose up -d db
   ```

5. Run migrations:
   ```bash
   npm run migration:run
   ```

## Development

Start the development server:

```bash
npm run start:dev
```

The API will be available at `http://localhost:3000/api`

## API Documentation

Once the server is running, you can access the Swagger documentation at:

```
http://localhost:3000/api/docs
```

## Rate Limiting

The API implements rate limiting to protect against abuse:

- **Auth Endpoints**: 5 requests per minute
- **API/Exchange Endpoints**: 30 requests per minute
- **Admin Endpoints**: 50 requests per minute

## Testing

Run the test suite:

```bash
# Unit tests
npm run test

# e2e tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Docker Support

Build and run with Docker:

```bash
docker-compose up --build
```

## Project Structure

```
src/
├── common/           # Shared utilities and configurations
├── modules/          # Feature modules
│   ├── admin/       # Admin functionality
│   ├── auth/        # Authentication
│   ├── exchanges/   # Exchange integrations
│   ├── strategies/  # Trading strategies
│   └── users/       # User management
├── config/          # Application configuration
└── main.ts         # Application entry point
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, email <EMAIL> or open an issue in the repository.
