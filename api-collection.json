{"info": {"name": "Crypto Auto-Trading Platform API", "description": "API collection for the Crypto Auto-Trading Platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"ABC123\"\n}"}}}, {"name": "Resend Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["api", "auth", "resend-verification"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}, "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your-refresh-token\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}}}]}, {"name": "User", "item": [{"name": "API Keys", "item": [{"name": "Create API Key", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api-keys", "host": ["{{baseUrl}}"], "path": ["user", "api-keys"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Kraken Key\",\n  \"exchange\": \"kraken\",\n  \"apiKey\": \"your-api-key\",\n  \"apiSecret\": \"your-api-secret\"\n}"}}}, {"name": "Update API Key", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["user", "api-keys", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Key Name\",\n  \"isActive\": true\n}"}}}, {"name": "Delete API Key", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/user/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["user", "api-keys", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/user/api-keys", "host": ["{{baseUrl}}"], "path": ["user", "api-keys"]}}}]}, {"name": "Payments", "item": [{"name": "Add Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/payments", "host": ["{{baseUrl}}"], "path": ["user", "payments"]}, "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.00\n}"}}}, {"name": "List Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/user/payments", "host": ["{{baseUrl}}"], "path": ["user", "payments"]}}}]}, {"name": "Subscriptions", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/subscriptions", "host": ["{{baseUrl}}"], "path": ["user", "subscriptions"]}, "body": {"mode": "raw", "raw": "{\n  \"strategyId\": 1,\n  \"apiKeyId\": 1,\n  \"paymentId\": 1,\n  \"startDate\": \"2024-03-20T00:00:00Z\",\n  \"endDate\": \"2024-04-20T00:00:00Z\"\n}"}}}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/subscriptions/:id", "host": ["{{baseUrl}}"], "path": ["user", "subscriptions", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n  \"isActive\": true\n}"}}}, {"name": "Delete Subscription", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/user/subscriptions/:id", "host": ["{{baseUrl}}"], "path": ["user", "subscriptions", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/user/subscriptions", "host": ["{{baseUrl}}"], "path": ["user", "subscriptions"]}}}]}]}, {"name": "Admin", "item": [{"name": "Products", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/admin/products", "host": ["{{baseUrl}}"], "path": ["admin", "products"]}, "body": {"mode": "raw", "raw": "{\n  \"symbol\": \"BTC\",\n  \"name\": \"Bitcoin\",\n  \"priceUsd\": 50000.00\n}"}}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/admin/products/:id", "host": ["{{baseUrl}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n  \"priceUsd\": 51000.00,\n  \"isActive\": true\n}"}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/products/:id", "host": ["{{baseUrl}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Get Product", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/products/:id", "host": ["{{baseUrl}}"], "path": ["admin", "products", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List All Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/products", "host": ["{{baseUrl}}"], "path": ["admin", "products"]}}}]}, {"name": "Strategies", "item": [{"name": "Create Strategy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/admin/strategies", "host": ["{{baseUrl}}"], "path": ["admin", "strategies"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"BTC Long Strategy\",\n  \"buyAmount\": 1000.00,\n  \"profitPercentage\": 5.00,\n  \"exchange\": \"kraken\",\n  \"productId\": 1\n}"}}}, {"name": "Update Strategy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/admin/strategies/:id", "host": ["{{baseUrl}}"], "path": ["admin", "strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}, "body": {"mode": "raw", "raw": "{\n  \"profitPercentage\": 6.00,\n  \"isActive\": true\n}"}}}, {"name": "Delete Strategy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/strategies/:id", "host": ["{{baseUrl}}"], "path": ["admin", "strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "Get Strategy", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/strategies/:id", "host": ["{{baseUrl}}"], "path": ["admin", "strategies", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List All Strategies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/strategies", "host": ["{{baseUrl}}"], "path": ["admin", "strategies"]}}}]}, {"name": "User Management", "item": [{"name": "List All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users", "host": ["{{baseUrl}}"], "path": ["admin", "users"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users/search?email=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["admin", "users", "search"], "query": [{"key": "email", "value": "<EMAIL>"}]}}}, {"name": "Get User Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users/:id", "host": ["{{baseUrl}}"], "path": ["admin", "users", ":id"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List User API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users/:id/api-keys", "host": ["{{baseUrl}}"], "path": ["admin", "users", ":id", "api-keys"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List User Subscriptions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users/:id/subscriptions", "host": ["{{baseUrl}}"], "path": ["admin", "users", ":id", "subscriptions"], "variable": [{"key": "id", "value": "1"}]}}}, {"name": "List User Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/admin/users/:id/payments", "host": ["{{baseUrl}}"], "path": ["admin", "users", ":id", "payments"], "variable": [{"key": "id", "value": "1"}]}}}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000"}, {"key": "accessToken", "value": "your-access-token"}]}