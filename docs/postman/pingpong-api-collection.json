{"info": {"name": "PingPong API", "description": "Crypto auto-trading platform API collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication related endpoints", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securePassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login with email and password"}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email"]}, "description": "Verify user email with code"}}, {"name": "Resend Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["api", "auth", "resend-verification"]}, "description": "Resend verification code"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{refreshToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh"]}, "description": "Refresh access token"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}, "description": "Logout user"}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/profile", "host": ["{{baseUrl}}"], "path": ["api", "auth", "profile"]}, "description": "Get user profile"}}]}, {"name": "Exchanges", "description": "Exchange related endpoints", "item": [{"name": "Get Ticker", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/exchanges/ticker/BTCUSD?apiKey={{apiKey}}&apiSecret={{apiSecret}}&exchange=kraken", "host": ["{{baseUrl}}"], "path": ["api", "exchanges", "ticker", "BTCUSD"], "query": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"key": "apiSecret", "value": "{{apiSecret}}"}, {"key": "exchange", "value": "kraken"}]}, "description": "Get ticker information for a symbol"}}, {"name": "Get Order Book", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/exchanges/orderbook/BTCUSD?apiKey={{apiKey}}&apiSecret={{apiSecret}}&exchange=kraken", "host": ["{{baseUrl}}"], "path": ["api", "exchanges", "orderbook", "BTCUSD"], "query": [{"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"key": "apiSecret", "value": "{{apiSecret}}"}, {"key": "exchange", "value": "kraken"}]}, "description": "Get order book for a symbol"}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"symbol\": \"BTCUSD\",\n  \"type\": \"limit\",\n  \"side\": \"buy\",\n  \"amount\": 0.1,\n  \"price\": 50000,\n  \"apiKey\": \"{{apiKey}}\",\n  \"apiSecret\": \"{{apiSecret}}\",\n  \"exchange\": \"kraken\"\n}"}, "url": {"raw": "{{baseUrl}}/api/exchanges/orders", "host": ["{{baseUrl}}"], "path": ["api", "exchanges", "orders"]}, "description": "Create a new order"}}, {"name": "Get Balance", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/exchanges/balance?asset=BTC&apiKey={{apiKey}}&apiSecret={{apiSecret}}&exchange=kraken", "host": ["{{baseUrl}}"], "path": ["api", "exchanges", "balance"], "query": [{"key": "asset", "value": "BTC"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "{{a<PERSON><PERSON><PERSON>}}"}, {"key": "apiSecret", "value": "{{apiSecret}}"}, {"key": "exchange", "value": "kraken"}]}, "description": "Get account balance"}}]}, {"name": "Admin", "description": "Admin related endpoints", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/admin/users", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users"]}, "description": "Get all users (Admin only)"}}, {"name": "Update User Role", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role\": \"ADMIN\"\n}"}, "url": {"raw": "{{baseUrl}}/api/admin/users/1/role", "host": ["{{baseUrl}}"], "path": ["api", "admin", "users", "1", "role"]}, "description": "Update user role (Admin only)"}}]}, {"name": "Subscriptions", "description": "Subscription management endpoints", "item": [{"name": "Get All Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/plans", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "plans"]}, "description": "Get all available subscription plans"}}, {"name": "Subscribe to Plan", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"planId\": \"premium\",\n  \"paymentMethodId\": \"pm_123456789\"\n}"}, "url": {"raw": "{{baseUrl}}/api/subscriptions/subscribe", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "subscribe"]}, "description": "Subscribe to a plan"}}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/cancel", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "cancel"]}, "description": "Cancel current subscription"}}, {"name": "Get Current Subscription", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/current", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "current"]}, "description": "Get current subscription details"}}]}, {"name": "Payments", "description": "Payment management endpoints", "item": [{"name": "Add Payment Method", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"card\",\n  \"card\": {\n    \"number\": \"****************\",\n    \"exp_month\": 12,\n    \"exp_year\": 2025,\n    \"cvc\": \"123\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/methods", "host": ["{{baseUrl}}"], "path": ["api", "payments", "methods"]}, "description": "Add a new payment method"}}, {"name": "Get Payment Methods", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/methods", "host": ["{{baseUrl}}"], "path": ["api", "payments", "methods"]}, "description": "Get all payment methods"}}, {"name": "Delete Payment Method", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/methods/:id", "host": ["{{baseUrl}}"], "path": ["api", "payments", "methods", ":id"], "variable": [{"key": "id", "value": "pm_123456789"}]}, "description": "Delete a payment method"}}, {"name": "Get Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/history", "host": ["{{baseUrl}}"], "path": ["api", "payments", "history"]}, "description": "Get payment history"}}]}, {"name": "Strategies", "description": "Trading strategy management endpoints", "item": [{"name": "Create Strategy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"RSI Strategy\",\n  \"description\": \"RSI-based trading strategy\",\n  \"type\": \"RSI\",\n  \"parameters\": {\n    \"period\": 14,\n    \"overbought\": 70,\n    \"oversold\": 30\n  },\n  \"exchange\": \"kraken\",\n  \"symbol\": \"BTCUSD\",\n  \"apiKey\": \"{{apiKey}}\",\n  \"apiSecret\": \"{{apiSecret}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/strategies", "host": ["{{baseUrl}}"], "path": ["api", "strategies"]}, "description": "Create a new trading strategy"}}, {"name": "Get All Strategies", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/strategies", "host": ["{{baseUrl}}"], "path": ["api", "strategies"]}, "description": "Get all trading strategies"}}, {"name": "Get Strategy Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/strategies/:id", "host": ["{{baseUrl}}"], "path": ["api", "strategies", ":id"], "variable": [{"key": "id", "value": "strategy_id"}]}, "description": "Get strategy details"}}, {"name": "Update Strategy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated RSI Strategy\",\n  \"parameters\": {\n    \"period\": 21,\n    \"overbought\": 75,\n    \"oversold\": 25\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/strategies/:id", "host": ["{{baseUrl}}"], "path": ["api", "strategies", ":id"], "variable": [{"key": "id", "value": "strategy_id"}]}, "description": "Update a strategy"}}, {"name": "Delete Strategy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/strategies/:id", "host": ["{{baseUrl}}"], "path": ["api", "strategies", ":id"], "variable": [{"key": "id", "value": "strategy_id"}]}, "description": "Delete a strategy"}}, {"name": "Start Strategy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/strategies/:id/start", "host": ["{{baseUrl}}"], "path": ["api", "strategies", ":id", "start"], "variable": [{"key": "id", "value": "strategy_id"}]}, "description": "Start a strategy"}}, {"name": "Stop Strategy", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/strategies/:id/stop", "host": ["{{baseUrl}}"], "path": ["api", "strategies", ":id", "stop"], "variable": [{"key": "id", "value": "strategy_id"}]}, "description": "Stop a strategy"}}]}, {"name": "API Keys", "description": "Exchange API key management endpoints", "item": [{"name": "Add API Key", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"exchange\": \"kraken\",\n  \"apiKey\": \"your_api_key\",\n  \"apiSecret\": \"your_api_secret\",\n  \"label\": \"Kraken Main Account\"\n}"}, "url": {"raw": "{{baseUrl}}/api/api-keys", "host": ["{{baseUrl}}"], "path": ["api", "api-keys"]}, "description": "Add a new exchange API key"}}, {"name": "Get All API Keys", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/api-keys", "host": ["{{baseUrl}}"], "path": ["api", "api-keys"]}, "description": "Get all API keys"}}, {"name": "Update API Key", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"label\": \"Updated Label\",\n  \"apiKey\": \"new_api_key\",\n  \"apiSecret\": \"new_api_secret\"\n}"}, "url": {"raw": "{{baseUrl}}/api/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["api", "api-keys", ":id"], "variable": [{"key": "id", "value": "api_key_id"}]}, "description": "Update an API key"}}, {"name": "Delete API Key", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/api-keys/:id", "host": ["{{baseUrl}}"], "path": ["api", "api-keys", ":id"], "variable": [{"key": "id", "value": "api_key_id"}]}, "description": "Delete an API key"}}, {"name": "Test API Key", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"exchange\": \"kraken\",\n  \"api<PERSON>ey\": \"your_api_key\",\n  \"apiSecret\": \"your_api_secret\"\n}"}, "url": {"raw": "{{baseUrl}}/api/api-keys/test", "host": ["{{baseUrl}}"], "path": ["api", "api-keys", "test"]}, "description": "Test API key validity"}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "accessToken", "value": "your_access_token_here", "type": "string"}, {"key": "refreshToken", "value": "your_refresh_token_here", "type": "string"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "your_exchange_api_key_here", "type": "string"}, {"key": "apiSecret", "value": "your_exchange_api_secret_here", "type": "string"}]}