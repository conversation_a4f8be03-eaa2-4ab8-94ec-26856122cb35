# Authentication API

## Endpoints

### Register User

```http
POST /api/auth/register
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "<PERSON>",
  "lastName": "<PERSON><PERSON>"
}
```

**Response:**

```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "<PERSON><PERSON>",
  "isVerified": false,
  "role": "USER",
  "createdAt": "2024-03-20T10:00:00Z"
}
```

### Login

```http
POST /api/auth/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**

```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER"
  }
}
```

### Verify Email

```http
POST /api/auth/verify-email
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Response:**

```json
{
  "message": "Email verified successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "isVerified": true
  }
}
```

### Resend Verification Code

```http
POST /api/auth/resend-verification
```

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "message": "Verification code sent successfully"
}
```

### Refresh Token

```http
POST /api/auth/refresh
```

**Headers:**

```
Authorization: Bearer <refresh_token>
```

**Response:**

```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Logout

```http
POST /api/auth/logout
```

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "message": "Logged out successfully"
}
```

### Get Profile

```http
GET /api/auth/profile
```

**Headers:**

```
Authorization: Bearer <access_token>
```

**Response:**

```json
{
  "id": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "USER",
  "isVerified": true,
  "createdAt": "2024-03-20T10:00:00Z"
}
```

## Rate Limiting

- All auth endpoints are limited to 5 requests per minute per IP address.

## Error Responses

### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

### 429 Too Many Requests

```json
{
  "statusCode": 429,
  "message": "ThrottlerException: Too Many Requests",
  "error": "Too Many Requests"
}
```

### 400 Bad Request

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": ["email must be an email", "password must be longer than or equal to 8 characters"]
}
```
