{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/CVS": true}, "hide-files.files": [], "editor.codeActionsOnSave": {"source.organizeImports": "always", "source.fixAll.eslint": "always"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.preferences.importModuleSpecifier": "non-relative", "typescript.updateImportsOnFileMove.enabled": "always"}